# 🔐 Bedazzled Platform - Credentials Checklist

Use this checklist to ensure you have all the necessary credentials and services configured for the Bedazzled platform.

## ✅ Essential Credentials (Required for Basic Functionality)

### 1. Database (MongoDB)
- [ ] **MongoDB Connection String**
  - Local: `mongodb://localhost:27017/bedazzled`
  - Atlas: `mongodb+srv://username:<EMAIL>/bedazzled`
  - Variable: `MONGODB_URI`

### 2. Security (JWT)
- [ ] **JWT Secret Key** (64+ characters recommended)
  - Generate with: `node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"`
  - Variable: `JWT_SECRET`

### 3. Application URLs
- [ ] **Frontend URL**: `http://localhost:3000` (development)
- [ ] **Backend URL**: `http://localhost:3001/api` (development)
- [ ] **CORS Configuration**: Frontend URL in backend env

## 🔧 Authentication & Social Login

### 4. Google OAuth (Recommended)
- [ ] **Google Cloud Console Project**
  - Go to: https://console.cloud.google.com/
  - Enable: Google+ API, Google OAuth2 API
- [ ] **OAuth 2.0 Client ID**
  - Authorized redirect URIs: `http://localhost:3001/api/auth/google/callback`
  - Variables: `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`

## 📧 Email Services (For Notifications)

### 5. SMTP Configuration (Choose One)

#### Option A: Gmail SMTP (Easy for Development)
- [ ] **Gmail Account with 2FA**
- [ ] **App Password Generated**
  - Variables: `SMTP_HOST=smtp.gmail.com`, `SMTP_USER`, `SMTP_PASS`

#### Option B: SendGrid (Production Recommended)
- [ ] **SendGrid Account**: https://sendgrid.com/
- [ ] **API Key Generated**
  - Variable: `SENDGRID_API_KEY`

## 💳 Payment Processing (Optional - Future Implementation)

### 6. Stripe (Recommended)
- [ ] **Stripe Account**: https://stripe.com/
- [ ] **Test API Keys**
  - Variables: `STRIPE_SECRET_KEY`, `STRIPE_PUBLISHABLE_KEY`
- [ ] **Webhook Endpoint**: `http://localhost:3001/api/webhooks/stripe`

## ☁️ File Storage (Optional - Production)

### 7. Cloud Storage (Choose One)

#### Option A: AWS S3
- [ ] **AWS Account & S3 Bucket**
- [ ] **IAM User with S3 Permissions**
  - Variables: `AWS_ACCESS_KEY_ID`, `AWS_SECRET_ACCESS_KEY`, `AWS_S3_BUCKET`

#### Option B: Cloudinary
- [ ] **Cloudinary Account**: https://cloudinary.com/
- [ ] **API Credentials**
  - Variables: `CLOUDINARY_CLOUD_NAME`, `CLOUDINARY_API_KEY`, `CLOUDINARY_API_SECRET`

## 📊 Monitoring & Analytics (Optional)

### 8. Error Tracking
- [ ] **Sentry Account**: https://sentry.io/
- [ ] **DSN Configuration**
  - Variable: `SENTRY_DSN`

### 9. Analytics
- [ ] **Google Analytics**: https://analytics.google.com/
- [ ] **Tracking ID**
  - Variable: `NEXT_PUBLIC_GA_ID`

## 🚀 Quick Setup Commands

### 1. Validate Your Setup
```bash
# Run the environment validator
pnpm validate-env
```

### 2. Start Development Servers
```bash
# Terminal 1: Backend
cd backend
pnpm start:dev

# Terminal 2: Frontend  
pnpm dev
```

### 3. Test the Platform
- [ ] **Frontend loads**: http://localhost:3000
- [ ] **Backend API responds**: http://localhost:3001/api
- [ ] **Database connects**: Check backend console logs
- [ ] **Google OAuth works**: Try signing in
- [ ] **File upload works**: Try uploading in admin panel
- [ ] **Email sends**: Test custom order form

## 🔒 Security Checklist

### Development
- [ ] Use different secrets for development and production
- [ ] Never commit `.env` files to version control
- [ ] Use strong, randomly generated JWT secrets
- [ ] Enable CORS only for trusted domains

### Production
- [ ] Rotate all secrets before going live
- [ ] Use environment variable management (AWS Secrets Manager, etc.)
- [ ] Enable HTTPS for all endpoints
- [ ] Set up proper database access controls
- [ ] Configure rate limiting
- [ ] Set up monitoring and alerting

## 📝 Environment Files Summary

### Frontend (`.env.local`)
```bash
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id
# ... other frontend variables
```

### Backend (`backend/.env`)
```bash
MONGODB_URI=mongodb://localhost:27017/bedazzled
JWT_SECRET=your-super-secure-jwt-secret-64-chars-minimum
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FRONTEND_URL=http://localhost:3000
# ... other backend variables
```

## 🆘 Troubleshooting

### Common Issues
- **MongoDB connection failed**: Check if MongoDB is running locally
- **Google OAuth not working**: Verify redirect URIs match exactly
- **CORS errors**: Ensure frontend URL is in backend CORS config
- **File upload fails**: Check upload directory permissions
- **Email not sending**: Verify SMTP credentials and 2FA setup

### Getting Help
1. Run `pnpm validate-env` to check configuration
2. Check console logs for detailed error messages
3. Refer to `SETUP_GUIDE.md` for detailed instructions
4. Verify all services are running and accessible

## ✨ Success Indicators

When everything is working correctly, you should see:
- ✅ Frontend loads without errors
- ✅ Backend API responds to requests
- ✅ Database connection established
- ✅ Google OAuth login works
- ✅ File uploads save correctly
- ✅ Email notifications send (if configured)
- ✅ All tests pass

---

**🎉 Once all items are checked, your Bedazzled platform is ready for development!**
