version: 2

aliases:
  - &restore-cache
    restore_cache:
      key: dependency-cache-{{ checksum "package.json" }}
  - &install-deps
    run:
      name: Install dependencies
      command: npm install --ignore-scripts
  - &build-packages
    run:
      name: Build
      command: npm run build

jobs:
  build:
    working_directory: ~/nest
    docker:
      - image: cimg/node:22.3
    steps:
      - checkout
      - restore_cache:
          key: dependency-cache-{{ checksum "package.json" }}
      - run:
          name: Install dependencies
          command: npm install --ignore-scripts
      - save_cache:
          key: dependency-cache-{{ checksum "package.json" }}
          paths:
            - ./node_modules
      - run:
          name: Build
          command: npm run build

  e2e_test:
    working_directory: ~/nest
    docker:
      - image: cimg/node:22.3
    steps:
      - checkout
      - *restore-cache
      - *install-deps
      - run:
          name: E2E tests
          command: npm run test:e2e

workflows:
  version: 2
  build-and-test:
    jobs:
      - build
      - e2e_test:
          requires:
            - build
