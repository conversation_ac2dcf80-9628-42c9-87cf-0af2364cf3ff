# Installation
> `npm install --save @types/passport-google-oauth20`

# Summary
This package contains type definitions for passport-google-oauth20 (https://github.com/jaredhanson/passport-google-oauth2).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/passport-google-oauth20.

### Additional Details
 * Last updated: Sat, 04 May 2024 11:06:44 GMT
 * Dependencies: [@types/express](https://npmjs.com/package/@types/express), [@types/passport](https://npmjs.com/package/@types/passport), [@types/passport-oauth2](https://npmjs.com/package/@types/passport-oauth2)

# Credits
These definitions were written by [<PERSON><PERSON><PERSON>](https://github.com/yasupeke), [<PERSON>](https://github.com/ezintz), [<PERSON>](https://github.com/ngtan), and [<PERSON><PERSON><PERSON>](https://github.com/acerbic).
