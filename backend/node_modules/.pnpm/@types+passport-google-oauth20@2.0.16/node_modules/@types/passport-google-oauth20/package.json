{"name": "@types/passport-google-oauth20", "version": "2.0.16", "description": "TypeScript definitions for passport-google-oauth20", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/passport-google-oauth20", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/yasupeke"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ezintz"}, {"name": "<PERSON>", "githubUsername": "ngtan", "url": "https://github.com/ngtan"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "acerbic", "url": "https://github.com/acerbic"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/passport-google-oauth20"}, "scripts": {}, "dependencies": {"@types/express": "*", "@types/passport": "*", "@types/passport-oauth2": "*"}, "typesPublisherContentHash": "8a35570219d97dbb39cb7e7c0462ce0e58838712b18ee6d11f994cbb73c46005", "typeScriptVersion": "4.7"}