# Installation
> `npm install --save @types/passport-jwt`

# Summary
This package contains type definitions for passport-jwt (https://github.com/themikenicholson/passport-jwt).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/passport-jwt.

### Additional Details
 * Last updated: Fri, 26 Jan 2024 06:07:46 GMT
 * Dependencies: [@types/jsonwebtoken](https://npmjs.com/package/@types/jsonwebtoken), [@types/passport-strategy](https://npmjs.com/package/@types/passport-strategy)

# Credits
These definitions were written by [TANA<PERSON>](https://github.com/mugeso), [<PERSON>](https://github.com/alsiola), [<PERSON>](https://github.com/carlosscheffer), [<PERSON><PERSON><PERSON>](https://github.com/jindev), and [<PERSON><PERSON><PERSON><PERSON>](https://github.com/stbychkov).
