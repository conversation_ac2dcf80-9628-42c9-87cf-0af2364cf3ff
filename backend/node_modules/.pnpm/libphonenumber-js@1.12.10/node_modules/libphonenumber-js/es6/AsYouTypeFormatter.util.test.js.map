{"version": 3, "file": "AsYouTypeFormatter.util.test.js", "names": ["closeNonPairedParens", "stripNonPairedParens", "repeat", "describe", "it", "should", "equal"], "sources": ["../source/AsYouTypeFormatter.util.test.js"], "sourcesContent": ["import { closeNonPairedParens, stripNonPairedParens, repeat } from './AsYouTypeFormatter.util.js'\r\n\r\ndescribe('closeNonPairedParens', () => {\r\n\tit('should close non-paired braces', () => {\r\n\t\tcloseNonPairedParens('(000) 123-45 (9  )', 15).should.equal('(000) 123-45 (9  )')\r\n\t})\r\n})\r\n\r\ndescribe('stripNonPairedParens', () => {\r\n\tit('should strip non-paired braces', () => {\r\n\t\tstripNonPairedParens('(000) 123-45 (9').should.equal('(000) 123-45 9')\r\n\t\tstripNonPairedParens('(000) 123-45 (9)').should.equal('(000) 123-45 (9)')\r\n\t})\r\n})\r\n\r\ndescribe('repeat', () => {\r\n\tit('should repeat string N times', () => {\r\n\t\trepeat('a', 0).should.equal('')\r\n\t\trepeat('a', 3).should.equal('aaa')\r\n\t\trepeat('a', 4).should.equal('aaaa')\r\n\t})\r\n})"], "mappings": "AAAA,SAASA,oBAAT,EAA+BC,oBAA/B,EAAqDC,MAArD,QAAmE,8BAAnE;AAEAC,QAAQ,CAAC,sBAAD,EAAyB,YAAM;EACtCC,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1CJ,oBAAoB,CAAC,oBAAD,EAAuB,EAAvB,CAApB,CAA+CK,MAA/C,CAAsDC,KAAtD,CAA4D,oBAA5D;EACA,CAFC,CAAF;AAGA,CAJO,CAAR;AAMAH,QAAQ,CAAC,sBAAD,EAAyB,YAAM;EACtCC,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1CH,oBAAoB,CAAC,iBAAD,CAApB,CAAwCI,MAAxC,CAA+CC,KAA/C,CAAqD,gBAArD;IACAL,oBAAoB,CAAC,kBAAD,CAApB,CAAyCI,MAAzC,CAAgDC,KAAhD,CAAsD,kBAAtD;EACA,CAHC,CAAF;AAIA,CALO,CAAR;AAOAH,QAAQ,CAAC,QAAD,EAAW,YAAM;EACxBC,EAAE,CAAC,8BAAD,EAAiC,YAAM;IACxCF,MAAM,CAAC,GAAD,EAAM,CAAN,CAAN,CAAeG,MAAf,CAAsBC,KAAtB,CAA4B,EAA5B;IACAJ,MAAM,CAAC,GAAD,EAAM,CAAN,CAAN,CAAeG,MAAf,CAAsBC,KAAtB,CAA4B,KAA5B;IACAJ,MAAM,CAAC,GAAD,EAAM,CAAN,CAAN,CAAeG,MAAf,CAAsBC,KAAtB,CAA4B,MAA5B;EACA,CAJC,CAAF;AAKA,CANO,CAAR"}