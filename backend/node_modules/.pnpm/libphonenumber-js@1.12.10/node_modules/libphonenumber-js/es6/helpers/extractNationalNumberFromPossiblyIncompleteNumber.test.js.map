{"version": 3, "file": "extractNationalNumberFromPossiblyIncompleteNumber.test.js", "names": ["<PERSON><PERSON><PERSON>", "metadata", "type", "extractNationalNumberFromPossiblyIncompleteNumber", "describe", "it", "meta", "country", "should", "deep", "equal", "nationalPrefix", "undefined", "carrierCode", "nationalNumber"], "sources": ["../../source/helpers/extractNationalNumberFromPossiblyIncompleteNumber.test.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport metadata from '../../metadata.min.json' assert { type: 'json' }\r\nimport extractNationalNumberFromPossiblyIncompleteNumber from './extractNationalNumberFromPossiblyIncompleteNumber.js'\r\n\r\ndescribe('extractNationalNumberFromPossiblyIncompleteNumber', () => {\r\n\tit('should parse a carrier code when there is no national prefix transform rule', () => {\r\n\t\tconst meta = new Metadata(metadata)\r\n\t\tmeta.country('AU')\r\n\t\textractNationalNumberFromPossiblyIncompleteNumber('18311800123', meta).should.deep.equal({\r\n\t\t\tnationalPrefix: undefined,\r\n\t\t\tcarrierCode: '1831',\r\n\t\t\tnationalNumber: '1800123'\r\n\t\t})\r\n\t})\r\n})"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,gBAArB;AACA,OAAOC,QAAP,MAAqB,yBAArB,UAAwDC,IAAI,EAAE,MAA9D;AACA,OAAOC,iDAAP,MAA8D,wDAA9D;AAEAC,QAAQ,CAAC,mDAAD,EAAsD,YAAM;EACnEC,EAAE,CAAC,6EAAD,EAAgF,YAAM;IACvF,IAAMC,IAAI,GAAG,IAAIN,QAAJ,CAAaC,QAAb,CAAb;IACAK,IAAI,CAACC,OAAL,CAAa,IAAb;IACAJ,iDAAiD,CAAC,aAAD,EAAgBG,IAAhB,CAAjD,CAAuEE,MAAvE,CAA8EC,IAA9E,CAAmFC,KAAnF,CAAyF;MACxFC,cAAc,EAAEC,SADwE;MAExFC,WAAW,EAAE,MAF2E;MAGxFC,cAAc,EAAE;IAHwE,CAAzF;EAKA,CARC,CAAF;AASA,CAVO,CAAR"}