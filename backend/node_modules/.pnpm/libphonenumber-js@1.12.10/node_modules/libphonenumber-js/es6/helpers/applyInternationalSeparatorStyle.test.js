import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js';
describe('applyInternationalSeparatorStyle', function () {
  it('should change Google\'s international format style', function () {
    applyInternationalSeparatorStyle('(xxx) xxx-xx-xx').should.equal('xxx xxx xx xx');
    applyInternationalSeparatorStyle('(xxx)xxx').should.equal('xxx xxx');
  });
});
//# sourceMappingURL=applyInternationalSeparatorStyle.test.js.map