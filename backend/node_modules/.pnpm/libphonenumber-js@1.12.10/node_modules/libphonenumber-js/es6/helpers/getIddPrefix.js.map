{"version": 3, "file": "getIddPrefix.js", "names": ["<PERSON><PERSON><PERSON>", "SINGLE_IDD_PREFIX_REG_EXP", "getIddPrefix", "country", "callingCode", "metadata", "countryMetadata", "selectNumberingPlan", "defaultIDDPrefix", "test", "IDDPrefix"], "sources": ["../../source/helpers/getIddPrefix.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\n\r\n/**\r\n * <PERSON><PERSON> that makes it easy to distinguish whether a region has a single\r\n * international dialing prefix or not. If a region has a single international\r\n * prefix (e.g. 011 in USA), it will be represented as a string that contains\r\n * a sequence of ASCII digits, and possibly a tilde, which signals waiting for\r\n * the tone. If there are multiple available international prefixes in a\r\n * region, they will be represented as a regex string that always contains one\r\n * or more characters that are not ASCII digits or a tilde.\r\n */\r\nconst SINGLE_IDD_PREFIX_REG_EXP = /^[\\d]+(?:[~\\u2053\\u223C\\uFF5E][\\d]+)?$/\r\n\r\n// For regions that have multiple IDD prefixes\r\n// a preferred IDD prefix is returned.\r\nexport default function getIddPrefix(country, callingCode, metadata) {\r\n\tconst countryMetadata = new Metadata(metadata)\r\n\tcountryMetadata.selectNumberingPlan(country, callingCode)\r\n\tif (countryMetadata.defaultIDDPrefix()) {\r\n\t\treturn countryMetadata.defaultIDDPrefix()\r\n\t}\r\n\tif (SINGLE_IDD_PREFIX_REG_EXP.test(countryMetadata.IDDPrefix())) {\r\n\t\treturn countryMetadata.IDDPrefix()\r\n\t}\r\n}\r\n"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,gBAArB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMC,yBAAyB,GAAG,wCAAlC,C,CAEA;AACA;;AACA,eAAe,SAASC,YAAT,CAAsBC,OAAtB,EAA+BC,WAA/B,EAA4CC,QAA5C,EAAsD;EACpE,IAAMC,eAAe,GAAG,IAAIN,QAAJ,CAAaK,QAAb,CAAxB;EACAC,eAAe,CAACC,mBAAhB,CAAoCJ,OAApC,EAA6CC,WAA7C;;EACA,IAAIE,eAAe,CAACE,gBAAhB,EAAJ,EAAwC;IACvC,OAAOF,eAAe,CAACE,gBAAhB,EAAP;EACA;;EACD,IAAIP,yBAAyB,CAACQ,IAA1B,CAA+BH,eAAe,CAACI,SAAhB,EAA/B,CAAJ,EAAiE;IAChE,OAAOJ,eAAe,CAACI,SAAhB,EAAP;EACA;AACD"}