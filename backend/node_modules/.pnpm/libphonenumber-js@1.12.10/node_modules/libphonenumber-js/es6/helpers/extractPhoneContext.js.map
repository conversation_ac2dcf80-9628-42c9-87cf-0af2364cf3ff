{"version": 3, "file": "extractPhoneContext.js", "names": ["VALID_DIGITS", "PLUS_SIGN", "RFC3966_VISUAL_SEPARATOR_", "RFC3966_PHONE_DIGIT_", "RFC3966_GLOBAL_NUMBER_DIGITS_", "RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_", "RegExp", "ALPHANUM_", "RFC3966_DOMAINLABEL_", "VALID_ALPHA_", "RFC3966_TOPLABEL_", "RFC3966_DOMAINNAME_", "RFC3966_DOMAINNAME_PATTERN_", "RFC3966_PREFIX_", "RFC3966_PHONE_CONTEXT_", "RFC3966_ISDN_SUBADDRESS_", "extractPhoneContext", "numberToExtractFrom", "indexOfPhoneContext", "indexOf", "phoneContextStart", "length", "phoneContextEnd", "substring", "isPhoneContextValid", "phoneContext", "test"], "sources": ["../../source/helpers/extractPhoneContext.js"], "sourcesContent": ["// When phone numbers are written in `RFC3966` format — `\"tel:+12133734253\"` —\r\n// they can have their \"calling code\" part written separately in a `phone-context` parameter.\r\n// Example: `\"tel:12133734253;phone-context=+1\"`.\r\n// This function parses the full phone number from the local number and the `phone-context`\r\n// when the `phone-context` contains a `+` sign.\r\n\r\nimport {\r\n  VALID_DIGITS,\r\n  // PLUS_CHARS\r\n} from '../constants.js'\r\n\r\nexport const PLUS_SIGN = '+'\r\n\r\nconst RFC3966_VISUAL_SEPARATOR_ = '[\\\\-\\\\.\\\\(\\\\)]?'\r\n\r\nconst RFC3966_PHONE_DIGIT_ = '(' + '[' + VALID_DIGITS + ']' + '|' + RFC3966_VISUAL_SEPARATOR_ + ')'\r\n\r\nconst RFC3966_GLOBAL_NUMBER_DIGITS_ =\r\n\t'^' +\r\n\t'\\\\' +\r\n\tPLUS_SIGN +\r\n\tRFC3966_PHONE_DIGIT_ +\r\n\t'*' +\r\n\t'[' + VALID_DIGITS +  ']' +\r\n\tRFC3966_PHONE_DIGIT_ +\r\n\t'*' +\r\n\t'$'\r\n\r\n/**\r\n * Regular expression of valid global-number-digits for the phone-context\r\n * parameter, following the syntax defined in RFC3966.\r\n */\r\nconst RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_ = new RegExp(RFC3966_GLOBAL_NUMBER_DIGITS_, 'g')\r\n\r\n// In this port of Google's library, we don't accept alpha characters in phone numbers.\r\n// const ALPHANUM_ = VALID_ALPHA_ + VALID_DIGITS\r\nconst ALPHANUM_ = VALID_DIGITS\r\n\r\nconst RFC3966_DOMAINLABEL_ = '[' + ALPHANUM_ + ']+((\\\\-)*[' + ALPHANUM_ + '])*'\r\n\r\nconst VALID_ALPHA_ = 'a-zA-Z'\r\nconst RFC3966_TOPLABEL_ = '[' + VALID_ALPHA_ + ']+((\\\\-)*[' + ALPHANUM_ + '])*'\r\n\r\nconst RFC3966_DOMAINNAME_ = '^(' + RFC3966_DOMAINLABEL_ + '\\\\.)*' + RFC3966_TOPLABEL_ + '\\\\.?$'\r\n\r\n/**\r\n * Regular expression of valid domainname for the phone-context parameter,\r\n * following the syntax defined in RFC3966.\r\n */\r\nconst RFC3966_DOMAINNAME_PATTERN_ = new RegExp(RFC3966_DOMAINNAME_, 'g')\r\n\r\nexport const RFC3966_PREFIX_ = 'tel:'\r\nexport const RFC3966_PHONE_CONTEXT_ = ';phone-context='\r\nexport const RFC3966_ISDN_SUBADDRESS_ = ';isub='\r\n\r\n/**\r\n * Extracts the value of the phone-context parameter of `numberToExtractFrom`,\r\n * following the syntax defined in RFC3966.\r\n *\r\n * @param {string} numberToExtractFrom\r\n * @return {string|null} the extracted string (possibly empty), or `null` if no phone-context parameter is found.\r\n */\r\nexport default function extractPhoneContext(numberToExtractFrom) {\r\n\tconst indexOfPhoneContext = numberToExtractFrom.indexOf(RFC3966_PHONE_CONTEXT_)\r\n\t// If no phone-context parameter is present\r\n\tif (indexOfPhoneContext < 0) {\r\n\t\treturn null\r\n\t}\r\n\r\n\tconst phoneContextStart = indexOfPhoneContext + RFC3966_PHONE_CONTEXT_.length\r\n\t// If phone-context parameter is empty\r\n\tif (phoneContextStart >= numberToExtractFrom.length) {\r\n\t\treturn ''\r\n\t}\r\n\r\n\tconst phoneContextEnd = numberToExtractFrom.indexOf(';', phoneContextStart)\r\n\t// If phone-context is not the last parameter\r\n\tif (phoneContextEnd >= 0) {\r\n\t\treturn numberToExtractFrom.substring(phoneContextStart, phoneContextEnd)\r\n\t} else {\r\n\t\treturn numberToExtractFrom.substring(phoneContextStart)\r\n\t}\r\n}\r\n\r\n/**\r\n * Returns whether the value of phoneContext follows the syntax defined in RFC3966.\r\n *\r\n * @param {string|null} phoneContext\r\n * @return {boolean}\r\n */\r\nexport function isPhoneContextValid(phoneContext) {\r\n\tif (phoneContext === null) {\r\n\t\treturn true\r\n\t}\r\n\r\n\tif (phoneContext.length === 0) {\r\n\t\treturn false\r\n\t}\r\n\r\n\t// Does phone-context value match pattern of global-number-digits or domainname.\r\n\treturn RFC3966_GLOBAL_NUMBER_DIGITS_PATTERN_.test(phoneContext) ||\r\n\t\tRFC3966_DOMAINNAME_PATTERN_.test(phoneContext)\r\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AAEA,SACEA,YADF,CAEE;AAFF,OAGO,iBAHP;AAKA,OAAO,IAAMC,SAAS,GAAG,GAAlB;AAEP,IAAMC,yBAAyB,GAAG,iBAAlC;AAEA,IAAMC,oBAAoB,GAAG,MAAM,GAAN,GAAYH,YAAZ,GAA2B,GAA3B,GAAiC,GAAjC,GAAuCE,yBAAvC,GAAmE,GAAhG;AAEA,IAAME,6BAA6B,GAClC,MACA,IADA,GAEAH,SAFA,GAGAE,oBAHA,GAIA,GAJA,GAKA,GALA,GAKMH,YALN,GAKsB,GALtB,GAMAG,oBANA,GAOA,GAPA,GAQA,GATD;AAWA;AACA;AACA;AACA;;AACA,IAAME,qCAAqC,GAAG,IAAIC,MAAJ,CAAWF,6BAAX,EAA0C,GAA1C,CAA9C,C,CAEA;AACA;;AACA,IAAMG,SAAS,GAAGP,YAAlB;AAEA,IAAMQ,oBAAoB,GAAG,MAAMD,SAAN,GAAkB,YAAlB,GAAiCA,SAAjC,GAA6C,KAA1E;AAEA,IAAME,YAAY,GAAG,QAArB;AACA,IAAMC,iBAAiB,GAAG,MAAMD,YAAN,GAAqB,YAArB,GAAoCF,SAApC,GAAgD,KAA1E;AAEA,IAAMI,mBAAmB,GAAG,OAAOH,oBAAP,GAA8B,OAA9B,GAAwCE,iBAAxC,GAA4D,OAAxF;AAEA;AACA;AACA;AACA;;AACA,IAAME,2BAA2B,GAAG,IAAIN,MAAJ,CAAWK,mBAAX,EAAgC,GAAhC,CAApC;AAEA,OAAO,IAAME,eAAe,GAAG,MAAxB;AACP,OAAO,IAAMC,sBAAsB,GAAG,iBAA/B;AACP,OAAO,IAAMC,wBAAwB,GAAG,QAAjC;AAEP;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,mBAAT,CAA6BC,mBAA7B,EAAkD;EAChE,IAAMC,mBAAmB,GAAGD,mBAAmB,CAACE,OAApB,CAA4BL,sBAA5B,CAA5B,CADgE,CAEhE;;EACA,IAAII,mBAAmB,GAAG,CAA1B,EAA6B;IAC5B,OAAO,IAAP;EACA;;EAED,IAAME,iBAAiB,GAAGF,mBAAmB,GAAGJ,sBAAsB,CAACO,MAAvE,CAPgE,CAQhE;;EACA,IAAID,iBAAiB,IAAIH,mBAAmB,CAACI,MAA7C,EAAqD;IACpD,OAAO,EAAP;EACA;;EAED,IAAMC,eAAe,GAAGL,mBAAmB,CAACE,OAApB,CAA4B,GAA5B,EAAiCC,iBAAjC,CAAxB,CAbgE,CAchE;;EACA,IAAIE,eAAe,IAAI,CAAvB,EAA0B;IACzB,OAAOL,mBAAmB,CAACM,SAApB,CAA8BH,iBAA9B,EAAiDE,eAAjD,CAAP;EACA,CAFD,MAEO;IACN,OAAOL,mBAAmB,CAACM,SAApB,CAA8BH,iBAA9B,CAAP;EACA;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASI,mBAAT,CAA6BC,YAA7B,EAA2C;EACjD,IAAIA,YAAY,KAAK,IAArB,EAA2B;IAC1B,OAAO,IAAP;EACA;;EAED,IAAIA,YAAY,CAACJ,MAAb,KAAwB,CAA5B,EAA+B;IAC9B,OAAO,KAAP;EACA,CAPgD,CASjD;;;EACA,OAAOhB,qCAAqC,CAACqB,IAAtC,CAA2CD,YAA3C,KACNb,2BAA2B,CAACc,IAA5B,CAAiCD,YAAjC,CADD;AAEA"}