{"version": 3, "file": "AsYouTypeFormatter.PatternParser.test.js", "names": ["describe", "it", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parse", "should", "deep", "equal", "op", "args"], "sources": ["../source/AsYouTypeFormatter.PatternParser.test.js"], "sourcesContent": ["import PatternParser from './AsYouTypeFormatter.PatternParser.js'\r\n\r\ndescribe('PatternParser', function() {\r\n\tit('should parse single-character patterns', function() {\r\n\t\tnew PatternParser().parse('2').should.deep.equal('2')\r\n\t})\r\n\r\n\tit('should parse string patterns', function() {\r\n\t\tnew PatternParser().parse('123').should.deep.equal(['1', '2', '3'])\r\n\t})\r\n\r\n\tit('should parse \"one of\" patterns', function() {\r\n\t\tnew PatternParser().parse('[5-9]').should.deep.equal({\r\n\t\t\top: '[]',\r\n\t\t\targs: ['5', '6', '7', '8', '9']\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse \"or\" patterns', function() {\r\n\t\tnew PatternParser().parse('123|[5-9]').should.deep.equal({\r\n\t\t\top: '|',\r\n\t\t\targs: [\r\n\t\t\t\t['1', '2', '3'],\r\n\t\t\t\t{\r\n\t\t\t\t\top: '[]',\r\n\t\t\t\t\targs: ['5', '6', '7', '8', '9']\r\n\t\t\t\t}\r\n\t\t\t]\r\n\t\t})\r\n\r\n\t\tnew PatternParser().parse('123|[5-9]0').should.deep.equal({\r\n\t\t\top: '|',\r\n\t\t\targs: [\r\n\t\t\t\t['1', '2', '3'],\r\n\t\t\t\t[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\top: '[]',\r\n\t\t\t\t\t\targs: ['5', '6', '7', '8', '9']\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'0'\r\n\t\t\t\t]\r\n\t\t\t]\r\n\t\t})\r\n\t})\r\n\r\n\tit('should parse nested \"or\" patterns', function() {\r\n\t\tnew PatternParser().parse('123|(?:2|34)[5-9]').should.deep.equal({\r\n\t\t\top: '|',\r\n\t\t\targs: [\r\n\t\t\t\t['1', '2', '3'],\r\n\t\t\t\t[\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\top: '|',\r\n\t\t\t\t\t\targs: [\r\n\t\t\t\t\t\t\t'2',\r\n\t\t\t\t\t\t\t['3', '4']\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\top: '[]',\r\n\t\t\t\t\t\targs: ['5', '6', '7', '8', '9']\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t]\r\n\t\t})\r\n\t})\r\n})"], "mappings": ";;AAAA;;;;AAEAA,QAAQ,CAAC,eAAD,EAAkB,YAAW;EACpCC,EAAE,CAAC,wCAAD,EAA2C,YAAW;IACvD,IAAIC,2CAAJ,GAAoBC,KAApB,CAA0B,GAA1B,EAA+BC,MAA/B,CAAsCC,IAAtC,CAA2CC,KAA3C,CAAiD,GAAjD;EACA,CAFC,CAAF;EAIAL,EAAE,CAAC,8BAAD,EAAiC,YAAW;IAC7C,IAAIC,2CAAJ,GAAoBC,KAApB,CAA0B,KAA1B,EAAiCC,MAAjC,CAAwCC,IAAxC,CAA6CC,KAA7C,CAAmD,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CAAnD;EACA,CAFC,CAAF;EAIAL,EAAE,CAAC,gCAAD,EAAmC,YAAW;IAC/C,IAAIC,2CAAJ,GAAoBC,KAApB,CAA0B,OAA1B,EAAmCC,MAAnC,CAA0CC,IAA1C,CAA+CC,KAA/C,CAAqD;MACpDC,EAAE,EAAE,IADgD;MAEpDC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB;IAF8C,CAArD;EAIA,CALC,CAAF;EAOAP,EAAE,CAAC,4BAAD,EAA+B,YAAW;IAC3C,IAAIC,2CAAJ,GAAoBC,KAApB,CAA0B,WAA1B,EAAuCC,MAAvC,CAA8CC,IAA9C,CAAmDC,KAAnD,CAAyD;MACxDC,EAAE,EAAE,GADoD;MAExDC,IAAI,EAAE,CACL,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CADK,EAEL;QACCD,EAAE,EAAE,IADL;QAECC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB;MAFP,CAFK;IAFkD,CAAzD;IAWA,IAAIN,2CAAJ,GAAoBC,KAApB,CAA0B,YAA1B,EAAwCC,MAAxC,CAA+CC,IAA/C,CAAoDC,KAApD,CAA0D;MACzDC,EAAE,EAAE,GADqD;MAEzDC,IAAI,EAAE,CACL,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CADK,EAEL,CACC;QACCD,EAAE,EAAE,IADL;QAECC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB;MAFP,CADD,EAKC,GALD,CAFK;IAFmD,CAA1D;EAaA,CAzBC,CAAF;EA2BAP,EAAE,CAAC,mCAAD,EAAsC,YAAW;IAClD,IAAIC,2CAAJ,GAAoBC,KAApB,CAA0B,mBAA1B,EAA+CC,MAA/C,CAAsDC,IAAtD,CAA2DC,KAA3D,CAAiE;MAChEC,EAAE,EAAE,GAD4D;MAEhEC,IAAI,EAAE,CACL,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,CADK,EAEL,CACC;QACCD,EAAE,EAAE,GADL;QAECC,IAAI,EAAE,CACL,GADK,EAEL,CAAC,GAAD,EAAM,GAAN,CAFK;MAFP,CADD,EAQC;QACCD,EAAE,EAAE,IADL;QAECC,IAAI,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,GAAX,EAAgB,GAAhB,EAAqB,GAArB;MAFP,CARD,CAFK;IAF0D,CAAjE;EAmBA,CApBC,CAAF;AAqBA,CAhEO,CAAR"}