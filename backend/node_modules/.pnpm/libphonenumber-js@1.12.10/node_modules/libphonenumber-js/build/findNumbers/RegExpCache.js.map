{"version": 3, "file": "RegExpCache.js", "names": ["RegExpCache", "size", "cache", "L<PERSON><PERSON><PERSON>", "pattern", "regExp", "get", "RegExp", "put"], "sources": ["../../source/findNumbers/RegExpCache.js"], "sourcesContent": ["import LRUCache from './LRUCache.js'\r\n\r\n// A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\r\n// countries being used for the same doc with ~10 patterns for each country. Some pages will have\r\n// a lot more countries in use, but typically fewer numbers for each so expanding the cache for\r\n// that use-case won't have a lot of benefit.\r\nexport default class RegExpCache {\r\n\tconstructor(size) {\r\n\t\tthis.cache = new LRUCache(size)\r\n\t}\r\n\r\n\tgetPatternForRegExp(pattern) {\r\n\t\tlet regExp = this.cache.get(pattern)\r\n\t\tif (!regExp) {\r\n\t\t\tregExp = new RegExp('^' + pattern)\r\n\t\t\tthis.cache.put(pattern, regExp)\r\n\t\t}\r\n\t\treturn regExp\r\n\t}\r\n}"], "mappings": ";;;;;;;AAAA;;;;;;;;;;AAEA;AACA;AACA;AACA;IACqBA,W;EACpB,qBAAYC,IAAZ,EAAkB;IAAA;;IACjB,KAAKC,KAAL,GAAa,IAAIC,oBAAJ,CAAaF,IAAb,CAAb;EACA;;;;WAED,6BAAoBG,OAApB,EAA6B;MAC5B,IAAIC,MAAM,GAAG,KAAKH,KAAL,CAAWI,GAAX,CAAeF,OAAf,CAAb;;MACA,IAAI,CAACC,MAAL,EAAa;QACZA,MAAM,GAAG,IAAIE,MAAJ,CAAW,MAAMH,OAAjB,CAAT;QACA,KAAKF,KAAL,CAAWM,GAAX,CAAeJ,OAAf,EAAwBC,MAAxB;MACA;;MACD,OAAOA,MAAP;IACA"}