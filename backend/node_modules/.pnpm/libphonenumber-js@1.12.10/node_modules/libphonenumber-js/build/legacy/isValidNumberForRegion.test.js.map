{"version": 3, "file": "isValidNumberForRegion.test.js", "names": ["isValidNumberForRegion", "parameters", "push", "metadata", "isValidNumberForRegionCustom", "apply", "describe", "it", "should", "equal", "expect", "phone", "country", "to", "_isValidNumberForRegion"], "sources": ["../../source/legacy/isValidNumberForRegion.test.js"], "sourcesContent": ["import metadata from '../../metadata.min.json' assert { type: 'json' }\r\nimport isValidNumberForRegionCustom from './isValidNumberForRegion.js'\r\nimport _isValidNumberForRegion from './isValidNumberForRegion_.js'\r\n\r\nfunction isValidNumberForRegion(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn isValidNumberForRegionCustom.apply(this, parameters)\r\n}\r\n\r\ndescribe('isValidNumberForRegion', () => {\r\n\tit('should detect if is valid number for region', () => {\r\n\t\tisValidNumberForRegion('07624369230', 'GB').should.equal(false)\r\n\t\tisValidNumberForRegion('07624369230', 'IM').should.equal(true)\r\n\t})\r\n\r\n\tit('should validate arguments', () => {\r\n\t\texpect(() => isValidNumberForRegion({ phone: '7624369230', country: 'GB' })).to.throw('number must be a string')\r\n\t\texpect(() => isValidNumberForRegion('7624369230')).to.throw('country must be a string')\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\t// Not a \"viable\" phone number.\r\n\t\tisValidNumberForRegion('7', 'GB').should.equal(false)\r\n\r\n\t\t// `options` argument `if/else` coverage.\r\n\t\t_isValidNumberForRegion('07624369230', 'GB', {}, metadata).should.equal(false)\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;AACA;;;;AAEA,SAASA,sBAAT,GAA+C;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EAC9CA,UAAU,CAACC,IAAX,CAAgBC,uBAAhB;EACA,OAAOC,mCAAA,CAA6BC,KAA7B,CAAmC,IAAnC,EAAyCJ,UAAzC,CAAP;AACA;;AAEDK,QAAQ,CAAC,wBAAD,EAA2B,YAAM;EACxCC,EAAE,CAAC,6CAAD,EAAgD,YAAM;IACvDP,sBAAsB,CAAC,aAAD,EAAgB,IAAhB,CAAtB,CAA4CQ,MAA5C,CAAmDC,KAAnD,CAAyD,KAAzD;IACAT,sBAAsB,CAAC,aAAD,EAAgB,IAAhB,CAAtB,CAA4CQ,MAA5C,CAAmDC,KAAnD,CAAyD,IAAzD;EACA,CAHC,CAAF;EAKAF,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrCG,MAAM,CAAC;MAAA,OAAMV,sBAAsB,CAAC;QAAEW,KAAK,EAAE,YAAT;QAAuBC,OAAO,EAAE;MAAhC,CAAD,CAA5B;IAAA,CAAD,CAAN,CAA6EC,EAA7E,UAAsF,yBAAtF;IACAH,MAAM,CAAC;MAAA,OAAMV,sBAAsB,CAAC,YAAD,CAA5B;IAAA,CAAD,CAAN,CAAmDa,EAAnD,UAA4D,0BAA5D;EACA,CAHC,CAAF;EAKAN,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC;IACAP,sBAAsB,CAAC,GAAD,EAAM,IAAN,CAAtB,CAAkCQ,MAAlC,CAAyCC,KAAzC,CAA+C,KAA/C,EAFqC,CAIrC;;IACA,IAAAK,mCAAA,EAAwB,aAAxB,EAAuC,IAAvC,EAA6C,EAA7C,EAAiDX,uBAAjD,EAA2DK,MAA3D,CAAkEC,KAAlE,CAAwE,KAAxE;EACA,CANC,CAAF;AAOA,CAlBO,CAAR"}