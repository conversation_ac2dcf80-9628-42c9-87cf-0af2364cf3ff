{"version": 3, "file": "isValidNumber.js", "names": ["isValidNumber", "normalizeArguments", "arguments", "input", "options", "metadata", "phone", "_isValidNumber"], "sources": ["../../source/legacy/isValidNumber.js"], "sourcesContent": ["import _isValidNumber from '../isValid.js'\r\nimport { normalizeArguments } from './getNumberType.js'\r\n\r\n// Finds out national phone number type (fixed line, mobile, etc)\r\nexport default function isValidNumber() {\r\n\tconst { input, options, metadata } = normalizeArguments(arguments)\r\n\t// `parseNumber()` would return `{}` when no phone number could be parsed from the input.\r\n\tif (!input.phone) {\r\n\t\treturn false\r\n\t}\r\n\treturn _isValidNumber(input, options, metadata)\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEA;AACe,SAASA,aAAT,GAAyB;EACvC,0BAAqC,IAAAC,iCAAA,EAAmBC,SAAnB,CAArC;EAAA,IAAQC,KAAR,uBAAQA,KAAR;EAAA,IAAeC,OAAf,uBAAeA,OAAf;EAAA,IAAwBC,QAAxB,uBAAwBA,QAAxB,CADuC,CAEvC;;;EACA,IAAI,CAACF,KAAK,CAACG,KAAX,EAAkB;IACjB,OAAO,KAAP;EACA;;EACD,OAAO,IAAAC,mBAAA,EAAeJ,KAAf,EAAsBC,OAAtB,EAA+BC,QAA/B,CAAP;AACA"}