{"version": 3, "file": "searchNumbers.js", "names": ["searchNumbers", "normalizeArguments", "arguments", "text", "options", "metadata", "matcher", "PhoneNumberMatcher", "Symbol", "iterator", "next", "hasNext", "done", "value"], "sources": ["../../source/legacy/searchNumbers.js"], "sourcesContent": ["import normalizeArguments from '../normalizeArguments.js'\r\nimport PhoneNumberMatcher from '../PhoneNumberMatcher.js'\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport default function searchNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\r\n\tconst matcher = new PhoneNumberMatcher(text, options, metadata)\r\n\r\n\treturn  {\r\n\t\t[Symbol.iterator]() {\r\n\t\t\treturn {\r\n\t    \t\tnext: () => {\r\n\t    \t\t\tif (matcher.hasNext()) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tdone: false,\r\n\t\t\t\t\t\t\tvalue: matcher.next()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdone: true\r\n\t\t\t\t\t}\r\n\t    \t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n"], "mappings": ";;;;;;;AAAA;;AACA;;;;;;AAEA;AACA;AACA;AACe,SAASA,aAAT,GACf;EACC,0BAAoC,IAAAC,+BAAA,EAAmBC,SAAnB,CAApC;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EAEA,IAAMC,OAAO,GAAG,IAAIC,8BAAJ,CAAuBJ,IAAvB,EAA6BC,OAA7B,EAAsCC,QAAtC,CAAhB;EAEA,2BACEG,MAAM,CAACC,QADT,cACqB;IACnB,OAAO;MACHC,IAAI,EAAE,gBAAM;QACX,IAAIJ,OAAO,CAACK,OAAR,EAAJ,EAAuB;UACzB,OAAO;YACNC,IAAI,EAAE,KADA;YAENC,KAAK,EAAEP,OAAO,CAACI,IAAR;UAFD,CAAP;QAIA;;QACD,OAAO;UACNE,IAAI,EAAE;QADA,CAAP;MAGG;IAXE,CAAP;EAaA,CAfF;AAiBA"}