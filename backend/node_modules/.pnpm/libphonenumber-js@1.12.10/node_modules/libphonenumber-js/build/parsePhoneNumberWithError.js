"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = parsePhoneNumberWithError;

var _parsePhoneNumberWithError_ = _interopRequireDefault(require("./parsePhoneNumberWithError_.js"));

var _normalizeArguments2 = _interopRequireDefault(require("./normalizeArguments.js"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

function parsePhoneNumberWithError() {
  var _normalizeArguments = (0, _normalizeArguments2["default"])(arguments),
      text = _normalizeArguments.text,
      options = _normalizeArguments.options,
      metadata = _normalizeArguments.metadata;

  return (0, _parsePhoneNumberWithError_["default"])(text, options, metadata);
}
//# sourceMappingURL=parsePhoneNumberWithError.js.map