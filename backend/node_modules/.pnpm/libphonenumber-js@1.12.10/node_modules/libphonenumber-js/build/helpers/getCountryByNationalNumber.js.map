{"version": 3, "file": "getCountryByNationalNumber.js", "names": ["getCountryByNationalNumber", "nationalPhoneNumber", "countries", "defaultCountry", "metadata", "<PERSON><PERSON><PERSON>", "country", "leadingDigits", "search", "getNumberType", "phone", "undefined"], "sources": ["../../source/helpers/getCountryByNationalNumber.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport getNumberType from './getNumberType.js'\r\n\r\nexport default function getCountryByNationalNumber(nationalPhoneNumber, {\r\n\tcountries,\r\n\tdefaultCountry,\r\n\tmetadata\r\n}) {\r\n\t// Re-create `metadata` because it will be selecting a `country`.\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\t// const matchingCountries = []\r\n\r\n\tfor (const country of countries) {\r\n\t\tmetadata.country(country)\r\n\t\t// \"Leading digits\" patterns are only defined for about 20% of all countries.\r\n\t\t// By definition, matching \"leading digits\" is a sufficient but not a necessary\r\n\t\t// condition for a phone number to belong to a country.\r\n\t\t// The point of \"leading digits\" check is that it's the fastest one to get a match.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits\r\n\t\t// I'd suppose that \"leading digits\" patterns are mutually exclusive for different countries\r\n\t\t// because of the intended use of that feature.\r\n\t\tif (metadata.leadingDigits()) {\r\n\t\t\tif (nationalPhoneNumber &&\r\n\t\t\t\tnationalPhoneNumber.search(metadata.leadingDigits()) === 0) {\r\n\t\t\t\treturn country\r\n\t\t\t}\r\n\t\t}\r\n\t\t// Else perform full validation with all of those\r\n\t\t// fixed-line/mobile/etc regular expressions.\r\n\t\telse if (getNumberType({ phone: nationalPhoneNumber, country }, undefined, metadata.metadata)) {\r\n\t\t\t// If both the `defaultCountry` and the \"main\" one match the phone number,\r\n\t\t\t// don't prefer the `defaultCountry` over the \"main\" one.\r\n\t\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/154\r\n\t\t\treturn country\r\n\t\t\t// // If the `defaultCountry` is among the `matchingCountries` then return it.\r\n\t\t\t// if (defaultCountry) {\r\n\t\t\t// \tif (country === defaultCountry) {\r\n\t\t\t// \t\treturn country\r\n\t\t\t// \t}\r\n\t\t\t// \tmatchingCountries.push(country)\r\n\t\t\t// } else {\r\n\t\t\t// \treturn country\r\n\t\t\t// }\r\n\t\t}\r\n\t}\r\n\r\n\t// // Return the first (\"main\") one of the `matchingCountries`.\r\n\t// if (matchingCountries.length > 0) {\r\n\t// \treturn matchingCountries[0]\r\n\t// }\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;;;;;;;AAEe,SAASA,0BAAT,CAAoCC,mBAApC,QAIZ;EAAA,IAHFC,SAGE,QAHFA,SAGE;EAAA,IAFFC,cAEE,QAFFA,cAEE;EAAA,IADFC,QACE,QADFA,QACE;EACF;EACAA,QAAQ,GAAG,IAAIC,oBAAJ,CAAaD,QAAb,CAAX,CAFE,CAIF;;EAEA,qDAAsBF,SAAtB,wCAAiC;IAAA,IAAtBI,OAAsB;IAChCF,QAAQ,CAACE,OAAT,CAAiBA,OAAjB,EADgC,CAEhC;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,IAAIF,QAAQ,CAACG,aAAT,EAAJ,EAA8B;MAC7B,IAAIN,mBAAmB,IACtBA,mBAAmB,CAACO,MAApB,CAA2BJ,QAAQ,CAACG,aAAT,EAA3B,MAAyD,CAD1D,EAC6D;QAC5D,OAAOD,OAAP;MACA;IACD,CALD,CAMA;IACA;IAPA,KAQK,IAAI,IAAAG,yBAAA,EAAc;MAAEC,KAAK,EAAET,mBAAT;MAA8BK,OAAO,EAAPA;IAA9B,CAAd,EAAuDK,SAAvD,EAAkEP,QAAQ,CAACA,QAA3E,CAAJ,EAA0F;MAC9F;MACA;MACA;MACA,OAAOE,OAAP,CAJ8F,CAK9F;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACD,CAtCC,CAwCF;EACA;EACA;EACA;;AACA"}