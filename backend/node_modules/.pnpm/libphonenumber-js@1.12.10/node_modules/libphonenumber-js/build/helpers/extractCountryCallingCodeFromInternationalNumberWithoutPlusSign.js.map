{"version": 3, "file": "extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js", "names": ["extractCountryCallingCodeFromInternationalNumberWithoutPlusSign", "number", "country", "callingCode", "metadata", "countryCallingCode", "getCountryCallingCode", "indexOf", "<PERSON><PERSON><PERSON>", "selectNumberingPlan", "possibleShorterNumber", "slice", "length", "extractNationalNumber", "possibleShorterNationalNumber", "nationalNumber", "matchesEntirely", "nationalNumberPattern", "checkNumberLength"], "sources": ["../../source/helpers/extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport matchesEntirely from './matchesEntirely.js'\r\nimport extractNationalNumber from './extractNationalNumber.js'\r\nimport checkNumberLength from './checkNumberLength.js'\r\nimport getCountryCallingCode from '../getCountryCallingCode.js'\r\n\r\n/**\r\n * Sometimes some people incorrectly input international phone numbers\r\n * without the leading `+`. This function corrects such input.\r\n * @param  {string} number — Phone number digits.\r\n * @param  {string?} country\r\n * @param  {string?} callingCode\r\n * @param  {object} metadata\r\n * @return {object} `{ countryCallingCode: string?, number: string }`.\r\n */\r\nexport default function extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(\r\n\tnumber,\r\n\tcountry,\r\n\tcallingCode,\r\n\tmetadata\r\n) {\r\n\tconst countryCallingCode = country ? getCountryCallingCode(country, metadata) : callingCode\r\n\tif (number.indexOf(countryCallingCode) === 0) {\r\n\t\tmetadata = new Metadata(metadata)\r\n\t\tmetadata.selectNumberingPlan(country, callingCode)\r\n\t\tconst possibleShorterNumber = number.slice(countryCallingCode.length)\r\n\t\tconst {\r\n\t\t\tnationalNumber: possibleShorterNationalNumber,\r\n\t\t} = extractNationalNumber(\r\n\t\t\tpossibleShorterNumber,\r\n\t\t\tmetadata\r\n\t\t)\r\n\t\tconst {\r\n\t\t\tnationalNumber\r\n\t\t} = extractNationalNumber(\r\n\t\t\tnumber,\r\n\t\t\tmetadata\r\n\t\t)\r\n\t\t// If the number was not valid before but is valid now,\r\n\t\t// or if it was too long before, we consider the number\r\n\t\t// with the country calling code stripped to be a better result\r\n\t\t// and keep that instead.\r\n\t\t// For example, in Germany (+49), `49` is a valid area code,\r\n\t\t// so if a number starts with `49`, it could be both a valid\r\n\t\t// national German number or an international number without\r\n\t\t// a leading `+`.\r\n\t\tif (\r\n\t\t\t(\r\n\t\t\t\t!matchesEntirely(nationalNumber, metadata.nationalNumberPattern())\r\n\t\t\t\t&&\r\n\t\t\t\tmatchesEntirely(possibleShorterNationalNumber, metadata.nationalNumberPattern())\r\n\t\t\t)\r\n\t\t\t||\r\n\t\t\tcheckNumberLength(nationalNumber, metadata) === 'TOO_LONG'\r\n\t\t) {\r\n\t\t\treturn {\r\n\t\t\t\tcountryCallingCode,\r\n\t\t\t\tnumber: possibleShorterNumber\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn { number }\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASA,+DAAT,CACdC,MADc,EAEdC,OAFc,EAGdC,WAHc,EAIdC,QAJc,EAKb;EACD,IAAMC,kBAAkB,GAAGH,OAAO,GAAG,IAAAI,iCAAA,EAAsBJ,OAAtB,EAA+BE,QAA/B,CAAH,GAA8CD,WAAhF;;EACA,IAAIF,MAAM,CAACM,OAAP,CAAeF,kBAAf,MAAuC,CAA3C,EAA8C;IAC7CD,QAAQ,GAAG,IAAII,oBAAJ,CAAaJ,QAAb,CAAX;IACAA,QAAQ,CAACK,mBAAT,CAA6BP,OAA7B,EAAsCC,WAAtC;IACA,IAAMO,qBAAqB,GAAGT,MAAM,CAACU,KAAP,CAAaN,kBAAkB,CAACO,MAAhC,CAA9B;;IACA,4BAEI,IAAAC,iCAAA,EACHH,qBADG,EAEHN,QAFG,CAFJ;IAAA,IACiBU,6BADjB,yBACCC,cADD;;IAMA,6BAEI,IAAAF,iCAAA,EACHZ,MADG,EAEHG,QAFG,CAFJ;IAAA,IACCW,cADD,0BACCA,cADD,CAV6C,CAgB7C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,IAEE,CAAC,IAAAC,2BAAA,EAAgBD,cAAhB,EAAgCX,QAAQ,CAACa,qBAAT,EAAhC,CAAD,IAEA,IAAAD,2BAAA,EAAgBF,6BAAhB,EAA+CV,QAAQ,CAACa,qBAAT,EAA/C,CAHD,IAMA,IAAAC,6BAAA,EAAkBH,cAAlB,EAAkCX,QAAlC,MAAgD,UAPjD,EAQE;MACD,OAAO;QACNC,kBAAkB,EAAlBA,kBADM;QAENJ,MAAM,EAAES;MAFF,CAAP;IAIA;EACD;;EACD,OAAO;IAAET,MAAM,EAANA;EAAF,CAAP;AACA"}