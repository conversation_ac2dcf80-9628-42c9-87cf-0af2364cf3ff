{"version": 3, "file": "AsYouTypeParser.js", "names": ["VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART", "VALID_PUNCTUATION", "VALID_DIGITS", "VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN", "RegExp", "VALID_FORMATTED_PHONE_NUMBER_PART", "PLUS_CHARS", "AFTER_PHONE_NUMBER_DIGITS_END_PATTERN", "COMPLEX_NATIONAL_PREFIX", "AsYouType<PERSON><PERSON><PERSON>", "defaultCountry", "defaultCallingCode", "metadata", "onNationalSignificantNumberChange", "text", "state", "extractFormattedDigitsAndPlus", "formattedDigits", "hasPlus", "digits", "parseDigits", "justLeadingPlus", "startInternationalNumber", "inputDigits", "nextDigits", "hasReceivedThreeLeadingDigits", "length", "appendDigits", "extractIddPrefix", "isWaitingForCountryCallingCode", "extractCountryCallingCode", "appendNationalSignificantNumberDigits", "international", "hasExtractedNationalSignificantNumber", "extractNationalSignificantNumber", "getNationalDigits", "stateUpdate", "update", "callingCode", "getDigitsWithoutInternationalPrefix", "countryCallingCode", "number", "setCallingCode", "nationalSignificantNumber", "numberingPlan", "hasSelectedNumberingPlan", "nationalPrefixForParsing", "_nationalPrefixForParsing", "couldPossiblyExtractAnotherNationalSignificantNumber", "test", "undefined", "nationalDigits", "setState", "extractNationalNumberFromPossiblyIncompleteNumber", "nationalPrefix", "nationalNumber", "carrierCode", "onExtractedNationalNumber", "prevNationalSignificantNumber", "complexPrefixBeforeNationalSignificantNumber", "nationalSignificantNumberMatchesInput", "nationalSignificantNumberIndex", "lastIndexOf", "prefixBeforeNationalNumber", "slice", "extractAnotherNationalSignificantNumber", "extractCallingCodeAndNationalSignificantNumber", "fixMissingPlus", "IDDPrefix", "numberWithoutIDD", "stripIddPrefix", "country", "extractCountryCallingCodeFromInternationalNumberWithoutPlusSign", "newCallingCode", "missingPlus", "resetNationalSignificantNumber", "extractFormattedPhoneNumber", "startsAt", "search", "replace", "_extractFormattedDigitsAndPlus", "extractedNumber"], "sources": ["../source/AsYouTypeParser.js"], "sourcesContent": ["import extractCountryCallingCode from './helpers/extractCountryCallingCode.js'\r\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './helpers/extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js'\r\nimport extractNationalNumberFromPossiblyIncompleteNumber from './helpers/extractNationalNumberFromPossiblyIncompleteNumber.js'\r\nimport stripIddPrefix from './helpers/stripIddPrefix.js'\r\nimport parseDigits from './helpers/parseDigits.js'\r\n\r\nimport {\r\n\tVALID_DIGITS,\r\n\tVALID_PUNCTUATION,\r\n\tPLUS_CHARS\r\n} from './constants.js'\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART =\r\n\t'[' +\r\n\t\tVALID_PUNCTUATION +\r\n\t\tVALID_DIGITS +\r\n\t']+'\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN = new RegExp('^' + VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART + '$', 'i')\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_PART =\r\n\t'(?:' +\r\n\t\t'[' + PLUS_CHARS + ']' +\r\n\t\t'[' +\r\n\t\t\tVALID_PUNCTUATION +\r\n\t\t\tVALID_DIGITS +\r\n\t\t']*' +\r\n\t\t'|' +\r\n\t\t'[' +\r\n\t\t\tVALID_PUNCTUATION +\r\n\t\t\tVALID_DIGITS +\r\n\t\t']+' +\r\n\t')'\r\n\r\nconst AFTER_PHONE_NUMBER_DIGITS_END_PATTERN = new RegExp(\r\n\t'[^' +\r\n\t\tVALID_PUNCTUATION +\r\n\t\tVALID_DIGITS +\r\n\t']+' +\r\n\t'.*' +\r\n\t'$'\r\n)\r\n\r\n// Tests whether `national_prefix_for_parsing` could match\r\n// different national prefixes.\r\n// Matches anything that's not a digit or a square bracket.\r\nconst COMPLEX_NATIONAL_PREFIX = /[^\\d\\[\\]]/\r\n\r\nexport default class AsYouTypeParser {\r\n\tconstructor({\r\n\t\tdefaultCountry,\r\n\t\tdefaultCallingCode,\r\n\t\tmetadata,\r\n\t\tonNationalSignificantNumberChange\r\n\t}) {\r\n\t\tthis.defaultCountry = defaultCountry\r\n\t\tthis.defaultCallingCode = defaultCallingCode\r\n\t\tthis.metadata = metadata\r\n\t\tthis.onNationalSignificantNumberChange = onNationalSignificantNumberChange\r\n\t}\r\n\r\n\tinput(text, state) {\r\n\t\tconst [formattedDigits, hasPlus] = extractFormattedDigitsAndPlus(text)\r\n\t\tconst digits = parseDigits(formattedDigits)\r\n\t\t// Checks for a special case: just a leading `+` has been entered.\r\n\t\tlet justLeadingPlus\r\n\t\tif (hasPlus) {\r\n\t\t\tif (!state.digits) {\r\n\t\t\t\tstate.startInternationalNumber()\r\n\t\t\t\tif (!digits) {\r\n\t\t\t\t\tjustLeadingPlus = true\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (digits) {\r\n\t\t\tthis.inputDigits(digits, state)\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tdigits,\r\n\t\t\tjustLeadingPlus\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Inputs \"next\" phone number digits.\r\n\t * @param  {string} digits\r\n\t * @return {string} [formattedNumber] Formatted national phone number (if it can be formatted at this stage). Returning `undefined` means \"don't format the national phone number at this stage\".\r\n\t */\r\n\tinputDigits(nextDigits, state) {\r\n\t\tconst { digits } = state\r\n\t\tconst hasReceivedThreeLeadingDigits = digits.length < 3 && digits.length + nextDigits.length >= 3\r\n\r\n\t\t// Append phone number digits.\r\n\t\tstate.appendDigits(nextDigits)\r\n\r\n\t\t// Attempt to extract IDD prefix:\r\n\t\t// Some users input their phone number in international format,\r\n\t\t// but in an \"out-of-country\" dialing format instead of using the leading `+`.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/185\r\n\t\t// Detect such numbers as soon as there're at least 3 digits.\r\n\t\t// Google's library attempts to extract IDD prefix at 3 digits,\r\n\t\t// so this library just copies that behavior.\r\n\t\t// I guess that's because the most commot IDD prefixes are\r\n\t\t// `00` (Europe) and `011` (US).\r\n\t\t// There exist really long IDD prefixes too:\r\n\t\t// for example, in Australia the default IDD prefix is `0011`,\r\n\t\t// and it could even be as long as `14880011`.\r\n\t\t// An IDD prefix is extracted here, and then every time when\r\n\t\t// there's a new digit and the number couldn't be formatted.\r\n\t\tif (hasReceivedThreeLeadingDigits) {\r\n\t\t\tthis.extractIddPrefix(state)\r\n\t\t}\r\n\r\n\t\tif (this.isWaitingForCountryCallingCode(state)) {\r\n\t\t\tif (!this.extractCountryCallingCode(state)) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tstate.appendNationalSignificantNumberDigits(nextDigits)\r\n\t\t}\r\n\r\n\t\t// If a phone number is being input in international format,\r\n\t\t// then it's not valid for it to have a national prefix.\r\n\t\t// Still, some people incorrectly input such numbers with a national prefix.\r\n\t\t// In such cases, only attempt to strip a national prefix if the number becomes too long.\r\n\t\t// (but that is done later, not here)\r\n\t\tif (!state.international) {\r\n\t\t\tif (!this.hasExtractedNationalSignificantNumber) {\r\n\t\t\t\tthis.extractNationalSignificantNumber(\r\n\t\t\t\t\tstate.getNationalDigits(),\r\n\t\t\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tisWaitingForCountryCallingCode({ international, callingCode }) {\r\n\t\treturn international && !callingCode\r\n\t}\r\n\r\n\t// Extracts a country calling code from a number\r\n\t// being entered in internatonal format.\r\n\textractCountryCallingCode(state) {\r\n\t\tconst { countryCallingCode, number } = extractCountryCallingCode(\r\n\t\t\t'+' + state.getDigitsWithoutInternationalPrefix(),\r\n\t\t\tthis.defaultCountry,\r\n\t\t\tthis.defaultCallingCode,\r\n\t\t\tthis.metadata.metadata\r\n\t\t)\r\n\t\tif (countryCallingCode) {\r\n\t\t\tstate.setCallingCode(countryCallingCode)\r\n\t\t\tstate.update({\r\n\t\t\t\tnationalSignificantNumber: number\r\n\t\t\t})\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\treset(numberingPlan) {\r\n\t\tif (numberingPlan) {\r\n\t\t\tthis.hasSelectedNumberingPlan = true\r\n\t\t\tconst nationalPrefixForParsing = numberingPlan._nationalPrefixForParsing()\r\n\t\t\tthis.couldPossiblyExtractAnotherNationalSignificantNumber = nationalPrefixForParsing && COMPLEX_NATIONAL_PREFIX.test(nationalPrefixForParsing)\r\n\t\t} else {\r\n\t\t\tthis.hasSelectedNumberingPlan = undefined\r\n\t\t\tthis.couldPossiblyExtractAnotherNationalSignificantNumber = undefined\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Extracts a national (significant) number from user input.\r\n\t * Google's library is different in that it only applies `national_prefix_for_parsing`\r\n\t * and doesn't apply `national_prefix_transform_rule` after that.\r\n\t * https://github.com/google/libphonenumber/blob/a3d70b0487875475e6ad659af404943211d26456/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L539\r\n\t * @return {boolean} [extracted]\r\n\t */\r\n\textractNationalSignificantNumber(nationalDigits, setState) {\r\n\t\tif (!this.hasSelectedNumberingPlan) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tconst {\r\n\t\t\tnationalPrefix,\r\n\t\t\tnationalNumber,\r\n\t\t\tcarrierCode\r\n\t\t} = extractNationalNumberFromPossiblyIncompleteNumber(\r\n\t\t\tnationalDigits,\r\n\t\t\tthis.metadata\r\n\t\t)\r\n\t\tif (nationalNumber === nationalDigits) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tthis.onExtractedNationalNumber(\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalNumber,\r\n\t\t\tnationalDigits,\r\n\t\t\tsetState\r\n\t\t)\r\n\t\treturn true\r\n\t}\r\n\r\n\t/**\r\n\t * In Google's code this function is called \"attempt to extract longer NDD\".\r\n\t * \"Some national prefixes are a substring of others\", they say.\r\n\t * @return {boolean} [result] — Returns `true` if extracting a national prefix produced different results from what they were.\r\n\t */\r\n\textractAnotherNationalSignificantNumber(nationalDigits, prevNationalSignificantNumber, setState) {\r\n\t\tif (!this.hasExtractedNationalSignificantNumber) {\r\n\t\t\treturn this.extractNationalSignificantNumber(nationalDigits, setState)\r\n\t\t}\r\n\t\tif (!this.couldPossiblyExtractAnotherNationalSignificantNumber) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tconst {\r\n\t\t\tnationalPrefix,\r\n\t\t\tnationalNumber,\r\n\t\t\tcarrierCode\r\n\t\t} = extractNationalNumberFromPossiblyIncompleteNumber(\r\n\t\t\tnationalDigits,\r\n\t\t\tthis.metadata\r\n\t\t)\r\n\t\t// If a national prefix has been extracted previously,\r\n\t\t// then it's always extracted as additional digits are added.\r\n\t\t// That's assuming `extractNationalNumberFromPossiblyIncompleteNumber()`\r\n\t\t// doesn't do anything different from what it currently does.\r\n\t\t// So, just in case, here's this check, though it doesn't occur.\r\n\t\t/* istanbul ignore if */\r\n\t\tif (nationalNumber === prevNationalSignificantNumber) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tthis.onExtractedNationalNumber(\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalNumber,\r\n\t\t\tnationalDigits,\r\n\t\t\tsetState\r\n\t\t)\r\n\t\treturn true\r\n\t}\r\n\r\n\tonExtractedNationalNumber(\r\n\t\tnationalPrefix,\r\n\t\tcarrierCode,\r\n\t\tnationalSignificantNumber,\r\n\t\tnationalDigits,\r\n\t\tsetState\r\n\t) {\r\n\t\tlet complexPrefixBeforeNationalSignificantNumber\r\n\t\tlet nationalSignificantNumberMatchesInput\r\n\t\t// This check also works with empty `this.nationalSignificantNumber`.\r\n\t\tconst nationalSignificantNumberIndex = nationalDigits.lastIndexOf(nationalSignificantNumber)\r\n\t\t// If the extracted national (significant) number is the\r\n\t\t// last substring of the `digits`, then it means that it hasn't been altered:\r\n\t\t// no digits have been removed from the national (significant) number\r\n\t\t// while applying `national_prefix_transform_rule`.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\r\n\t\tif (nationalSignificantNumberIndex >= 0 &&\r\n\t\t\tnationalSignificantNumberIndex === nationalDigits.length - nationalSignificantNumber.length) {\r\n\t\t\tnationalSignificantNumberMatchesInput = true\r\n\t\t\t// If a prefix of a national (significant) number is not as simple\r\n\t\t\t// as just a basic national prefix, then such prefix is stored in\r\n\t\t\t// `this.complexPrefixBeforeNationalSignificantNumber` property and will be\r\n\t\t\t// prepended \"as is\" to the national (significant) number to produce\r\n\t\t\t// a formatted result.\r\n\t\t\tconst prefixBeforeNationalNumber = nationalDigits.slice(0, nationalSignificantNumberIndex)\r\n\t\t\t// `prefixBeforeNationalNumber` is always non-empty,\r\n\t\t\t// because `onExtractedNationalNumber()` isn't called\r\n\t\t\t// when a national (significant) number hasn't been actually \"extracted\":\r\n\t\t\t// when a national (significant) number is equal to the national part of `digits`,\r\n\t\t\t// then `onExtractedNationalNumber()` doesn't get called.\r\n\t\t\tif (prefixBeforeNationalNumber !== nationalPrefix) {\r\n\t\t\t\tcomplexPrefixBeforeNationalSignificantNumber = prefixBeforeNationalNumber\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetState({\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalSignificantNumber,\r\n\t\t\tnationalSignificantNumberMatchesInput,\r\n\t\t\tcomplexPrefixBeforeNationalSignificantNumber\r\n\t\t})\r\n\t\t// `onExtractedNationalNumber()` is only called when\r\n\t\t// the national (significant) number actually did change.\r\n\t\tthis.hasExtractedNationalSignificantNumber = true\r\n\t\tthis.onNationalSignificantNumberChange()\r\n\t}\r\n\r\n\treExtractNationalSignificantNumber(state) {\r\n\t\t// Attempt to extract a national prefix.\r\n\t\t//\r\n\t\t// Some people incorrectly input national prefix\r\n\t\t// in an international phone number.\r\n\t\t// For example, some people write British phone numbers as `+44(0)...`.\r\n\t\t//\r\n\t\t// Also, in some rare cases, it is valid for a national prefix\r\n\t\t// to be a part of an international phone number.\r\n\t\t// For example, mobile phone numbers in Mexico are supposed to be\r\n\t\t// dialled internationally using a `1` national prefix,\r\n\t\t// so the national prefix will be part of an international number.\r\n\t\t//\r\n\t\t// Quote from:\r\n\t\t// https://www.mexperience.com/dialing-cell-phones-in-mexico/\r\n\t\t//\r\n\t\t// \"Dialing a Mexican cell phone from abroad\r\n\t\t// When you are calling a cell phone number in Mexico from outside Mexico,\r\n\t\t// it’s necessary to dial an additional “1” after Mexico’s country code\r\n\t\t// (which is “52”) and before the area code.\r\n\t\t// You also ignore the 045, and simply dial the area code and the\r\n\t\t// cell phone’s number.\r\n\t\t//\r\n\t\t// If you don’t add the “1”, you’ll receive a recorded announcement\r\n\t\t// asking you to redial using it.\r\n\t\t//\r\n\t\t// For example, if you are calling from the USA to a cell phone\r\n\t\t// in Mexico City, you would dial +52 – 1 – 55 – 1234 5678.\r\n\t\t// (Note that this is different to calling a land line in Mexico City\r\n\t\t// from abroad, where the number dialed would be +52 – 55 – 1234 5678)\".\r\n\t\t//\r\n\t\t// Google's demo output:\r\n\t\t// https://libphonenumber.appspot.com/phonenumberparser?number=%2b5215512345678&country=MX\r\n\t\t//\r\n\t\tif (this.extractAnotherNationalSignificantNumber(\r\n\t\t\tstate.getNationalDigits(),\r\n\t\t\tstate.nationalSignificantNumber,\r\n\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t)) {\r\n\t\t\treturn true\r\n\t\t}\r\n\t\t// If no format matches the phone number, then it could be\r\n\t\t// \"a really long IDD\" (quote from a comment in Google's library).\r\n\t\t// An IDD prefix is first extracted when the user has entered at least 3 digits,\r\n\t\t// and then here — every time when there's a new digit and the number\r\n\t\t// couldn't be formatted.\r\n\t\t// For example, in Australia the default IDD prefix is `0011`,\r\n\t\t// and it could even be as long as `14880011`.\r\n\t\t//\r\n\t\t// Could also check `!hasReceivedThreeLeadingDigits` here\r\n\t\t// to filter out the case when this check duplicates the one\r\n\t\t// already performed when there're 3 leading digits,\r\n\t\t// but it's not a big deal, and in most cases there\r\n\t\t// will be a suitable `format` when there're 3 leading digits.\r\n\t\t//\r\n\t\tif (this.extractIddPrefix(state)) {\r\n\t\t\tthis.extractCallingCodeAndNationalSignificantNumber(state)\r\n\t\t\treturn true\r\n\t\t}\r\n\t\t// Google's AsYouType formatter supports sort of an \"autocorrection\" feature\r\n\t\t// when it \"autocorrects\" numbers that have been input for a country\r\n\t\t// with that country's calling code.\r\n\t\t// Such \"autocorrection\" feature looks weird, but different people have been requesting it:\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/376\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/375\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/316\r\n\t\tif (this.fixMissingPlus(state)) {\r\n\t\t\tthis.extractCallingCodeAndNationalSignificantNumber(state)\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\textractIddPrefix(state) {\r\n\t\t// An IDD prefix can't be present in a number written with a `+`.\r\n\t\t// Also, don't re-extract an IDD prefix if has already been extracted.\r\n\t\tconst {\r\n\t\t\tinternational,\r\n\t\t\tIDDPrefix,\r\n\t\t\tdigits,\r\n\t\t\tnationalSignificantNumber\r\n\t\t} = state\r\n\t\tif (international || IDDPrefix) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// Some users input their phone number in \"out-of-country\"\r\n\t\t// dialing format instead of using the leading `+`.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/185\r\n\t\t// Detect such numbers.\r\n\t\tconst numberWithoutIDD = stripIddPrefix(\r\n\t\t\tdigits,\r\n\t\t\tthis.defaultCountry,\r\n\t\t\tthis.defaultCallingCode,\r\n\t\t\tthis.metadata.metadata\r\n\t\t)\r\n\t\tif (numberWithoutIDD !== undefined && numberWithoutIDD !== digits) {\r\n\t\t\t// If an IDD prefix was stripped then convert the IDD-prefixed number\r\n\t\t\t// to international number for subsequent parsing.\r\n\t\t\tstate.update({\r\n\t\t\t\tIDDPrefix: digits.slice(0, digits.length - numberWithoutIDD.length)\r\n\t\t\t})\r\n\t\t\tthis.startInternationalNumber(state, {\r\n\t\t\t\tcountry: undefined,\r\n\t\t\t\tcallingCode: undefined\r\n\t\t\t})\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\tfixMissingPlus(state) {\r\n\t\tif (!state.international) {\r\n\t\t\tconst {\r\n\t\t\t\tcountryCallingCode: newCallingCode,\r\n\t\t\t\tnumber\r\n\t\t\t} = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(\r\n\t\t\t\tstate.digits,\r\n\t\t\t\tthis.defaultCountry,\r\n\t\t\t\tthis.defaultCallingCode,\r\n\t\t\t\tthis.metadata.metadata\r\n\t\t\t)\r\n\t\t\tif (newCallingCode) {\r\n\t\t\t\tstate.update({\r\n\t\t\t\t\tmissingPlus: true\r\n\t\t\t\t})\r\n\t\t\t\tthis.startInternationalNumber(state, {\r\n\t\t\t\t\tcountry: state.country,\r\n\t\t\t\t\tcallingCode: newCallingCode\r\n\t\t\t\t})\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tstartInternationalNumber(state, { country, callingCode }) {\r\n\t\tstate.startInternationalNumber(country, callingCode)\r\n\t\t// If a national (significant) number has been extracted before, reset it.\r\n\t\tif (state.nationalSignificantNumber) {\r\n\t\t\tstate.resetNationalSignificantNumber()\r\n\t\t\tthis.onNationalSignificantNumberChange()\r\n\t\t\tthis.hasExtractedNationalSignificantNumber = undefined\r\n\t\t}\r\n\t}\r\n\r\n\textractCallingCodeAndNationalSignificantNumber(state) {\r\n\t\tif (this.extractCountryCallingCode(state)) {\r\n\t\t\t// `this.extractCallingCode()` is currently called when the number\r\n\t\t\t// couldn't be formatted during the standard procedure.\r\n\t\t\t// Normally, the national prefix would be re-extracted\r\n\t\t\t// for an international number if such number couldn't be formatted,\r\n\t\t\t// but since it's already not able to be formatted,\r\n\t\t\t// there won't be yet another retry, so also extract national prefix here.\r\n\t\t\tthis.extractNationalSignificantNumber(\r\n\t\t\t\tstate.getNationalDigits(),\r\n\t\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t\t)\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number from text (if there's any).\r\n * @param  {string} text\r\n * @return {string} [formattedPhoneNumber]\r\n */\r\nfunction extractFormattedPhoneNumber(text) {\r\n\t// Attempt to extract a possible number from the string passed in.\r\n\tconst startsAt = text.search(VALID_FORMATTED_PHONE_NUMBER_PART)\r\n\tif (startsAt < 0) {\r\n\t\treturn\r\n\t}\r\n\t// Trim everything to the left of the phone number.\r\n\ttext = text.slice(startsAt)\r\n\t// Trim the `+`.\r\n\tlet hasPlus\r\n\tif (text[0] === '+') {\r\n\t\thasPlus = true\r\n\t\ttext = text.slice('+'.length)\r\n\t}\r\n\t// Trim everything to the right of the phone number.\r\n\ttext = text.replace(AFTER_PHONE_NUMBER_DIGITS_END_PATTERN, '')\r\n\t// Re-add the previously trimmed `+`.\r\n\tif (hasPlus) {\r\n\t\ttext = '+' + text\r\n\t}\r\n\treturn text\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\r\nfunction _extractFormattedDigitsAndPlus(text) {\r\n\t// Extract a formatted phone number part from text.\r\n\tconst extractedNumber = extractFormattedPhoneNumber(text) || ''\r\n\t// Trim a `+`.\r\n\tif (extractedNumber[0] === '+') {\r\n\t\treturn [extractedNumber.slice('+'.length), true]\r\n\t}\r\n\treturn [extractedNumber]\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\r\nexport function extractFormattedDigitsAndPlus(text) {\r\n\tlet [formattedDigits, hasPlus] = _extractFormattedDigitsAndPlus(text)\r\n\t// If the extracted phone number part\r\n\t// can possibly be a part of some valid phone number\r\n\t// then parse phone number characters from a formatted phone number.\r\n\tif (!VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN.test(formattedDigits)) {\r\n\t\tformattedDigits = ''\r\n\t}\r\n\treturn [formattedDigits, hasPlus]\r\n}"], "mappings": ";;;;;;;;AAAA;;AACA;;AACA;;AACA;;AACA;;AAEA;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAMA,wCAAwC,GAC7C,MACCC,4BADD,GAECC,uBAFD,GAGA,IAJD;AAMA,IAAMC,gDAAgD,GAAG,IAAIC,MAAJ,CAAW,MAAMJ,wCAAN,GAAiD,GAA5D,EAAiE,GAAjE,CAAzD;AAEA,IAAMK,iCAAiC,GACtC,QACC,GADD,GACOC,qBADP,GACoB,GADpB,GAEC,GAFD,GAGEL,4BAHF,GAIEC,uBAJF,GAKC,IALD,GAMC,GAND,GAOC,GAPD,GAQED,4BARF,GASEC,uBATF,GAUC,IAVD,GAWA,GAZD;AAcA,IAAMK,qCAAqC,GAAG,IAAIH,MAAJ,CAC7C,OACCH,4BADD,GAECC,uBAFD,GAGA,IAHA,GAIA,IAJA,GAKA,GAN6C,CAA9C,C,CASA;AACA;AACA;;AACA,IAAMM,uBAAuB,GAAG,WAAhC;;IAEqBC,e;EACpB,+BAKG;IAAA,IAJFC,cAIE,QAJFA,cAIE;IAAA,IAHFC,kBAGE,QAHFA,kBAGE;IAAA,IAFFC,QAEE,QAFFA,QAEE;IAAA,IADFC,iCACE,QADFA,iCACE;;IAAA;;IACF,KAAKH,cAAL,GAAsBA,cAAtB;IACA,KAAKC,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,iCAAL,GAAyCA,iCAAzC;EACA;;;;WAED,eAAMC,IAAN,EAAYC,KAAZ,EAAmB;MAClB,4BAAmCC,6BAA6B,CAACF,IAAD,CAAhE;MAAA;MAAA,IAAOG,eAAP;MAAA,IAAwBC,OAAxB;;MACA,IAAMC,MAAM,GAAG,IAAAC,uBAAA,EAAYH,eAAZ,CAAf,CAFkB,CAGlB;;MACA,IAAII,eAAJ;;MACA,IAAIH,OAAJ,EAAa;QACZ,IAAI,CAACH,KAAK,CAACI,MAAX,EAAmB;UAClBJ,KAAK,CAACO,wBAAN;;UACA,IAAI,CAACH,MAAL,EAAa;YACZE,eAAe,GAAG,IAAlB;UACA;QACD;MACD;;MACD,IAAIF,MAAJ,EAAY;QACX,KAAKI,WAAL,CAAiBJ,MAAjB,EAAyBJ,KAAzB;MACA;;MACD,OAAO;QACNI,MAAM,EAANA,MADM;QAENE,eAAe,EAAfA;MAFM,CAAP;IAIA;IAED;AACD;AACA;AACA;AACA;;;;WACC,qBAAYG,UAAZ,EAAwBT,KAAxB,EAA+B;MAC9B,IAAQI,MAAR,GAAmBJ,KAAnB,CAAQI,MAAR;MACA,IAAMM,6BAA6B,GAAGN,MAAM,CAACO,MAAP,GAAgB,CAAhB,IAAqBP,MAAM,CAACO,MAAP,GAAgBF,UAAU,CAACE,MAA3B,IAAqC,CAAhG,CAF8B,CAI9B;;MACAX,KAAK,CAACY,YAAN,CAAmBH,UAAnB,EAL8B,CAO9B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MACA,IAAIC,6BAAJ,EAAmC;QAClC,KAAKG,gBAAL,CAAsBb,KAAtB;MACA;;MAED,IAAI,KAAKc,8BAAL,CAAoCd,KAApC,CAAJ,EAAgD;QAC/C,IAAI,CAAC,KAAKe,yBAAL,CAA+Bf,KAA/B,CAAL,EAA4C;UAC3C;QACA;MACD,CAJD,MAIO;QACNA,KAAK,CAACgB,qCAAN,CAA4CP,UAA5C;MACA,CA/B6B,CAiC9B;MACA;MACA;MACA;MACA;;;MACA,IAAI,CAACT,KAAK,CAACiB,aAAX,EAA0B;QACzB,IAAI,CAAC,KAAKC,qCAAV,EAAiD;UAChD,KAAKC,gCAAL,CACCnB,KAAK,CAACoB,iBAAN,EADD,EAEC,UAACC,WAAD;YAAA,OAAiBrB,KAAK,CAACsB,MAAN,CAAaD,WAAb,CAAjB;UAAA,CAFD;QAIA;MACD;IACD;;;WAED,+CAA+D;MAAA,IAA9BJ,aAA8B,SAA9BA,aAA8B;MAAA,IAAfM,WAAe,SAAfA,WAAe;MAC9D,OAAON,aAAa,IAAI,CAACM,WAAzB;IACA,C,CAED;IACA;;;;WACA,mCAA0BvB,KAA1B,EAAiC;MAChC,4BAAuC,IAAAe,sCAAA,EACtC,MAAMf,KAAK,CAACwB,mCAAN,EADgC,EAEtC,KAAK7B,cAFiC,EAGtC,KAAKC,kBAHiC,EAItC,KAAKC,QAAL,CAAcA,QAJwB,CAAvC;MAAA,IAAQ4B,kBAAR,yBAAQA,kBAAR;MAAA,IAA4BC,MAA5B,yBAA4BA,MAA5B;;MAMA,IAAID,kBAAJ,EAAwB;QACvBzB,KAAK,CAAC2B,cAAN,CAAqBF,kBAArB;QACAzB,KAAK,CAACsB,MAAN,CAAa;UACZM,yBAAyB,EAAEF;QADf,CAAb;QAGA,OAAO,IAAP;MACA;IACD;;;WAED,eAAMG,aAAN,EAAqB;MACpB,IAAIA,aAAJ,EAAmB;QAClB,KAAKC,wBAAL,GAAgC,IAAhC;;QACA,IAAMC,wBAAwB,GAAGF,aAAa,CAACG,yBAAd,EAAjC;;QACA,KAAKC,oDAAL,GAA4DF,wBAAwB,IAAItC,uBAAuB,CAACyC,IAAxB,CAA6BH,wBAA7B,CAAxF;MACA,CAJD,MAIO;QACN,KAAKD,wBAAL,GAAgCK,SAAhC;QACA,KAAKF,oDAAL,GAA4DE,SAA5D;MACA;IACD;IAED;AACD;AACA;AACA;AACA;AACA;AACA;;;;WACC,0CAAiCC,cAAjC,EAAiDC,QAAjD,EAA2D;MAC1D,IAAI,CAAC,KAAKP,wBAAV,EAAoC;QACnC;MACA;;MACD,4BAII,IAAAQ,6DAAA,EACHF,cADG,EAEH,KAAKvC,QAFF,CAJJ;MAAA,IACC0C,cADD,yBACCA,cADD;MAAA,IAECC,cAFD,yBAECA,cAFD;MAAA,IAGCC,WAHD,yBAGCA,WAHD;;MAQA,IAAID,cAAc,KAAKJ,cAAvB,EAAuC;QACtC;MACA;;MACD,KAAKM,yBAAL,CACCH,cADD,EAECE,WAFD,EAGCD,cAHD,EAICJ,cAJD,EAKCC,QALD;MAOA,OAAO,IAAP;IACA;IAED;AACD;AACA;AACA;AACA;;;;WACC,iDAAwCD,cAAxC,EAAwDO,6BAAxD,EAAuFN,QAAvF,EAAiG;MAChG,IAAI,CAAC,KAAKnB,qCAAV,EAAiD;QAChD,OAAO,KAAKC,gCAAL,CAAsCiB,cAAtC,EAAsDC,QAAtD,CAAP;MACA;;MACD,IAAI,CAAC,KAAKJ,oDAAV,EAAgE;QAC/D;MACA;;MACD,6BAII,IAAAK,6DAAA,EACHF,cADG,EAEH,KAAKvC,QAFF,CAJJ;MAAA,IACC0C,cADD,0BACCA,cADD;MAAA,IAECC,cAFD,0BAECA,cAFD;MAAA,IAGCC,WAHD,0BAGCA,WAHD,CAPgG,CAehG;MACA;MACA;MACA;MACA;;MACA;;;MACA,IAAID,cAAc,KAAKG,6BAAvB,EAAsD;QACrD;MACA;;MACD,KAAKD,yBAAL,CACCH,cADD,EAECE,WAFD,EAGCD,cAHD,EAICJ,cAJD,EAKCC,QALD;MAOA,OAAO,IAAP;IACA;;;WAED,mCACCE,cADD,EAECE,WAFD,EAGCb,yBAHD,EAICQ,cAJD,EAKCC,QALD,EAME;MACD,IAAIO,4CAAJ;MACA,IAAIC,qCAAJ,CAFC,CAGD;;MACA,IAAMC,8BAA8B,GAAGV,cAAc,CAACW,WAAf,CAA2BnB,yBAA3B,CAAvC,CAJC,CAKD;MACA;MACA;MACA;MACA;;MACA,IAAIkB,8BAA8B,IAAI,CAAlC,IACHA,8BAA8B,KAAKV,cAAc,CAACzB,MAAf,GAAwBiB,yBAAyB,CAACjB,MADtF,EAC8F;QAC7FkC,qCAAqC,GAAG,IAAxC,CAD6F,CAE7F;QACA;QACA;QACA;QACA;;QACA,IAAMG,0BAA0B,GAAGZ,cAAc,CAACa,KAAf,CAAqB,CAArB,EAAwBH,8BAAxB,CAAnC,CAP6F,CAQ7F;QACA;QACA;QACA;QACA;;QACA,IAAIE,0BAA0B,KAAKT,cAAnC,EAAmD;UAClDK,4CAA4C,GAAGI,0BAA/C;QACA;MACD;;MACDX,QAAQ,CAAC;QACRE,cAAc,EAAdA,cADQ;QAERE,WAAW,EAAXA,WAFQ;QAGRb,yBAAyB,EAAzBA,yBAHQ;QAIRiB,qCAAqC,EAArCA,qCAJQ;QAKRD,4CAA4C,EAA5CA;MALQ,CAAD,CAAR,CA5BC,CAmCD;MACA;;MACA,KAAK1B,qCAAL,GAA6C,IAA7C;MACA,KAAKpB,iCAAL;IACA;;;WAED,4CAAmCE,KAAnC,EAA0C;MACzC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,KAAKkD,uCAAL,CACHlD,KAAK,CAACoB,iBAAN,EADG,EAEHpB,KAAK,CAAC4B,yBAFH,EAGH,UAACP,WAAD;QAAA,OAAiBrB,KAAK,CAACsB,MAAN,CAAaD,WAAb,CAAjB;MAAA,CAHG,CAAJ,EAIG;QACF,OAAO,IAAP;MACA,CAxCwC,CAyCzC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;;MACA,IAAI,KAAKR,gBAAL,CAAsBb,KAAtB,CAAJ,EAAkC;QACjC,KAAKmD,8CAAL,CAAoDnD,KAApD;QACA,OAAO,IAAP;MACA,CA1DwC,CA2DzC;MACA;MACA;MACA;MACA;MACA;MACA;;;MACA,IAAI,KAAKoD,cAAL,CAAoBpD,KAApB,CAAJ,EAAgC;QAC/B,KAAKmD,8CAAL,CAAoDnD,KAApD;QACA,OAAO,IAAP;MACA;IACD;;;WAED,0BAAiBA,KAAjB,EAAwB;MACvB;MACA;MACA,IACCiB,aADD,GAKIjB,KALJ,CACCiB,aADD;MAAA,IAECoC,SAFD,GAKIrD,KALJ,CAECqD,SAFD;MAAA,IAGCjD,MAHD,GAKIJ,KALJ,CAGCI,MAHD;MAAA,IAICwB,yBAJD,GAKI5B,KALJ,CAIC4B,yBAJD;;MAMA,IAAIX,aAAa,IAAIoC,SAArB,EAAgC;QAC/B;MACA,CAXsB,CAYvB;MACA;MACA;MACA;;;MACA,IAAMC,gBAAgB,GAAG,IAAAC,0BAAA,EACxBnD,MADwB,EAExB,KAAKT,cAFmB,EAGxB,KAAKC,kBAHmB,EAIxB,KAAKC,QAAL,CAAcA,QAJU,CAAzB;;MAMA,IAAIyD,gBAAgB,KAAKnB,SAArB,IAAkCmB,gBAAgB,KAAKlD,MAA3D,EAAmE;QAClE;QACA;QACAJ,KAAK,CAACsB,MAAN,CAAa;UACZ+B,SAAS,EAAEjD,MAAM,CAAC6C,KAAP,CAAa,CAAb,EAAgB7C,MAAM,CAACO,MAAP,GAAgB2C,gBAAgB,CAAC3C,MAAjD;QADC,CAAb;QAGA,KAAKJ,wBAAL,CAA8BP,KAA9B,EAAqC;UACpCwD,OAAO,EAAErB,SAD2B;UAEpCZ,WAAW,EAAEY;QAFuB,CAArC;QAIA,OAAO,IAAP;MACA;IACD;;;WAED,wBAAenC,KAAf,EAAsB;MACrB,IAAI,CAACA,KAAK,CAACiB,aAAX,EAA0B;QACzB,6BAGI,IAAAwC,2EAAA,EACHzD,KAAK,CAACI,MADH,EAEH,KAAKT,cAFF,EAGH,KAAKC,kBAHF,EAIH,KAAKC,QAAL,CAAcA,QAJX,CAHJ;QAAA,IACqB6D,cADrB,0BACCjC,kBADD;QAAA,IAECC,MAFD,0BAECA,MAFD;;QASA,IAAIgC,cAAJ,EAAoB;UACnB1D,KAAK,CAACsB,MAAN,CAAa;YACZqC,WAAW,EAAE;UADD,CAAb;UAGA,KAAKpD,wBAAL,CAA8BP,KAA9B,EAAqC;YACpCwD,OAAO,EAAExD,KAAK,CAACwD,OADqB;YAEpCjC,WAAW,EAAEmC;UAFuB,CAArC;UAIA,OAAO,IAAP;QACA;MACD;IACD;;;WAED,kCAAyB1D,KAAzB,SAA0D;MAAA,IAAxBwD,OAAwB,SAAxBA,OAAwB;MAAA,IAAfjC,WAAe,SAAfA,WAAe;MACzDvB,KAAK,CAACO,wBAAN,CAA+BiD,OAA/B,EAAwCjC,WAAxC,EADyD,CAEzD;;MACA,IAAIvB,KAAK,CAAC4B,yBAAV,EAAqC;QACpC5B,KAAK,CAAC4D,8BAAN;QACA,KAAK9D,iCAAL;QACA,KAAKoB,qCAAL,GAA6CiB,SAA7C;MACA;IACD;;;WAED,wDAA+CnC,KAA/C,EAAsD;MACrD,IAAI,KAAKe,yBAAL,CAA+Bf,KAA/B,CAAJ,EAA2C;QAC1C;QACA;QACA;QACA;QACA;QACA;QACA,KAAKmB,gCAAL,CACCnB,KAAK,CAACoB,iBAAN,EADD,EAEC,UAACC,WAAD;UAAA,OAAiBrB,KAAK,CAACsB,MAAN,CAAaD,WAAb,CAAjB;QAAA,CAFD;MAIA;IACD;;;;;AAGF;AACA;AACA;AACA;AACA;;;;;AACA,SAASwC,2BAAT,CAAqC9D,IAArC,EAA2C;EAC1C;EACA,IAAM+D,QAAQ,GAAG/D,IAAI,CAACgE,MAAL,CAAYzE,iCAAZ,CAAjB;;EACA,IAAIwE,QAAQ,GAAG,CAAf,EAAkB;IACjB;EACA,CALyC,CAM1C;;;EACA/D,IAAI,GAAGA,IAAI,CAACkD,KAAL,CAAWa,QAAX,CAAP,CAP0C,CAQ1C;;EACA,IAAI3D,OAAJ;;EACA,IAAIJ,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAhB,EAAqB;IACpBI,OAAO,GAAG,IAAV;IACAJ,IAAI,GAAGA,IAAI,CAACkD,KAAL,CAAW,IAAItC,MAAf,CAAP;EACA,CAbyC,CAc1C;;;EACAZ,IAAI,GAAGA,IAAI,CAACiE,OAAL,CAAaxE,qCAAb,EAAoD,EAApD,CAAP,CAf0C,CAgB1C;;EACA,IAAIW,OAAJ,EAAa;IACZJ,IAAI,GAAG,MAAMA,IAAb;EACA;;EACD,OAAOA,IAAP;AACA;AAED;AACA;AACA;AACA;AACA;;;AACA,SAASkE,8BAAT,CAAwClE,IAAxC,EAA8C;EAC7C;EACA,IAAMmE,eAAe,GAAGL,2BAA2B,CAAC9D,IAAD,CAA3B,IAAqC,EAA7D,CAF6C,CAG7C;;EACA,IAAImE,eAAe,CAAC,CAAD,CAAf,KAAuB,GAA3B,EAAgC;IAC/B,OAAO,CAACA,eAAe,CAACjB,KAAhB,CAAsB,IAAItC,MAA1B,CAAD,EAAoC,IAApC,CAAP;EACA;;EACD,OAAO,CAACuD,eAAD,CAAP;AACA;AAED;AACA;AACA;AACA;AACA;;;AACO,SAASjE,6BAAT,CAAuCF,IAAvC,EAA6C;EACnD,6BAAiCkE,8BAA8B,CAAClE,IAAD,CAA/D;EAAA;EAAA,IAAKG,eAAL;EAAA,IAAsBC,OAAtB,6BADmD,CAEnD;EACA;EACA;;;EACA,IAAI,CAACf,gDAAgD,CAAC8C,IAAjD,CAAsDhC,eAAtD,CAAL,EAA6E;IAC5EA,eAAe,GAAG,EAAlB;EACA;;EACD,OAAO,CAACA,eAAD,EAAkBC,OAAlB,CAAP;AACA"}