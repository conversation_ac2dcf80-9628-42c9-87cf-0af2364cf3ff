{"version": 3, "file": "isValidPhoneNumber.test.js", "names": ["isValidPhoneNumber", "parameters", "push", "metadata", "_isValidPhoneNumber", "apply", "describe", "it", "should", "equal", "defaultCountry"], "sources": ["../source/isValidPhoneNumber.test.js"], "sourcesContent": ["import _isValidPhoneNumber from './isValidPhoneNumber.js'\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\nfunction isValidPhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _isValidPhoneNumber.apply(this, parameters)\r\n}\r\n\r\ndescribe('isValidPhoneNumber', () => {\r\n\tit('should detect whether a phone number is valid', () => {\r\n\t\tisValidPhoneNumber('8 (800) 555 35 35', 'RU').should.equal(true)\r\n\t\tisValidPhoneNumber('8 (800) 555 35 35 0', 'RU').should.equal(false)\r\n\t\tisValidPhoneNumber('Call: 8 (800) 555 35 35', 'RU').should.equal(false)\r\n\t\tisValidPhoneNumber('8 (800) 555 35 35', { defaultCountry: 'RU' }).should.equal(true)\r\n\t\tisValidPhoneNumber('+7 (800) 555 35 35').should.equal(true)\r\n\t\tisValidPhoneNumber('**** (800) 555 35 35').should.equal(false)\r\n\t\tisValidPhoneNumber(' +7 (800) 555 35 35').should.equal(false)\r\n\t\tisValidPhoneNumber(' ').should.equal(false)\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;;;AAEA,SAASA,kBAAT,GAA2C;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EAC1CA,UAAU,CAACC,IAAX,CAAgBC,uBAAhB;EACA,OAAOC,+BAAA,CAAoBC,KAApB,CAA0B,IAA1B,EAAgCJ,UAAhC,CAAP;AACA;;AAEDK,QAAQ,CAAC,oBAAD,EAAuB,YAAM;EACpCC,EAAE,CAAC,+CAAD,EAAkD,YAAM;IACzDP,kBAAkB,CAAC,mBAAD,EAAsB,IAAtB,CAAlB,CAA8CQ,MAA9C,CAAqDC,KAArD,CAA2D,IAA3D;IACAT,kBAAkB,CAAC,qBAAD,EAAwB,IAAxB,CAAlB,CAAgDQ,MAAhD,CAAuDC,KAAvD,CAA6D,KAA7D;IACAT,kBAAkB,CAAC,yBAAD,EAA4B,IAA5B,CAAlB,CAAoDQ,MAApD,CAA2DC,KAA3D,CAAiE,KAAjE;IACAT,kBAAkB,CAAC,mBAAD,EAAsB;MAAEU,cAAc,EAAE;IAAlB,CAAtB,CAAlB,CAAkEF,MAAlE,CAAyEC,KAAzE,CAA+E,IAA/E;IACAT,kBAAkB,CAAC,oBAAD,CAAlB,CAAyCQ,MAAzC,CAAgDC,KAAhD,CAAsD,IAAtD;IACAT,kBAAkB,CAAC,sBAAD,CAAlB,CAA2CQ,MAA3C,CAAkDC,KAAlD,CAAwD,KAAxD;IACAT,kBAAkB,CAAC,qBAAD,CAAlB,CAA0CQ,MAA1C,CAAiDC,KAAjD,CAAuD,KAAvD;IACAT,kBAAkB,CAAC,GAAD,CAAlB,CAAwBQ,MAAxB,CAA+BC,KAA/B,CAAqC,KAArC;EACA,CATC,CAAF;AAUA,CAXO,CAAR"}