{"version": 3, "file": "validatePhoneNumberLength.test.js", "names": ["validatePhoneNumberLength", "parameters", "push", "metadata", "_validatePhoneNumberLength", "apply", "describe", "it", "should", "equal", "expect", "to", "be", "undefined"], "sources": ["../source/validatePhoneNumberLength.test.js"], "sourcesContent": ["import _validatePhoneNumberLength from './validatePhoneNumberLength.js'\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\nfunction validatePhoneNumberLength(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _validatePhoneNumberLength.apply(this, parameters)\r\n}\r\n\r\ndescribe('validatePhoneNumberLength', () => {\r\n\tit('should detect whether a phone number length is valid', () => {\r\n\t\t// Not a phone number.\r\n\t\tvalidatePhoneNumberLength('+').should.equal('NOT_A_NUMBER')\r\n\t\tvalidatePhoneNumberLength('abcde').should.equal('NOT_A_NUMBER')\r\n\r\n\t\t// No country supplied for a national number.\r\n\t\tvalidatePhoneNumberLength('123').should.equal('INVALID_COUNTRY')\r\n\r\n\t\t// Too short while the number is not considered \"viable\"\r\n\t\t// by Google's `libphonenumber`.\r\n\t\tvalidatePhoneNumberLength('2', 'US').should.equal('TOO_SHORT')\r\n\t\tvalidatePhoneNumberLength('+1', 'US').should.equal('TOO_SHORT')\r\n\t\tvalidatePhoneNumberLength('+12', 'US').should.equal('TOO_SHORT')\r\n\r\n\t\t// Test national (significant) number length.\r\n\t\tvalidatePhoneNumberLength('444 1 44', 'TR').should.equal('TOO_SHORT')\r\n\t\texpect(validatePhoneNumberLength('444 1 444', 'TR')).to.be.undefined\r\n\t\tvalidatePhoneNumberLength('444 1 4444', 'TR').should.equal('INVALID_LENGTH')\r\n\t\tvalidatePhoneNumberLength('444 1 4444444444', 'TR').should.equal('TOO_LONG')\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;;;AAEA,SAASA,yBAAT,GAAkD;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EACjDA,UAAU,CAACC,IAAX,CAAgBC,uBAAhB;EACA,OAAOC,sCAAA,CAA2BC,KAA3B,CAAiC,IAAjC,EAAuCJ,UAAvC,CAAP;AACA;;AAEDK,QAAQ,CAAC,2BAAD,EAA8B,YAAM;EAC3CC,EAAE,CAAC,sDAAD,EAAyD,YAAM;IAChE;IACAP,yBAAyB,CAAC,GAAD,CAAzB,CAA+BQ,MAA/B,CAAsCC,KAAtC,CAA4C,cAA5C;IACAT,yBAAyB,CAAC,OAAD,CAAzB,CAAmCQ,MAAnC,CAA0CC,KAA1C,CAAgD,cAAhD,EAHgE,CAKhE;;IACAT,yBAAyB,CAAC,KAAD,CAAzB,CAAiCQ,MAAjC,CAAwCC,KAAxC,CAA8C,iBAA9C,EANgE,CAQhE;IACA;;IACAT,yBAAyB,CAAC,GAAD,EAAM,IAAN,CAAzB,CAAqCQ,MAArC,CAA4CC,KAA5C,CAAkD,WAAlD;IACAT,yBAAyB,CAAC,IAAD,EAAO,IAAP,CAAzB,CAAsCQ,MAAtC,CAA6CC,KAA7C,CAAmD,WAAnD;IACAT,yBAAyB,CAAC,KAAD,EAAQ,IAAR,CAAzB,CAAuCQ,MAAvC,CAA8CC,KAA9C,CAAoD,WAApD,EAZgE,CAchE;;IACAT,yBAAyB,CAAC,UAAD,EAAa,IAAb,CAAzB,CAA4CQ,MAA5C,CAAmDC,KAAnD,CAAyD,WAAzD;IACAC,MAAM,CAACV,yBAAyB,CAAC,WAAD,EAAc,IAAd,CAA1B,CAAN,CAAqDW,EAArD,CAAwDC,EAAxD,CAA2DC,SAA3D;IACAb,yBAAyB,CAAC,YAAD,EAAe,IAAf,CAAzB,CAA8CQ,MAA9C,CAAqDC,KAArD,CAA2D,gBAA3D;IACAT,yBAAyB,CAAC,kBAAD,EAAqB,IAArB,CAAzB,CAAoDQ,MAApD,CAA2DC,KAA3D,CAAiE,UAAjE;EACA,CAnBC,CAAF;AAoBA,CArBO,CAAR"}