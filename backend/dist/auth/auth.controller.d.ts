import { AuthService } from './auth.service';
import { CreateUserDto } from './dto/create-user.dto';
import { LoginDto } from './dto/login.dto';
import { User } from '../schemas/user.schema';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    register(createUserDto: CreateUserDto): Promise<{
        access_token: string;
        user: {
            id: unknown;
            email: string;
            name: string;
            role: string;
            avatar: string | undefined;
        };
    }>;
    login(loginDto: LoginDto): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            name: any;
            role: any;
            avatar: any;
        };
    }>;
    googleAuth(req: any): Promise<void>;
    googleAuthRedirect(req: any, res: any): Promise<void>;
    getProfile(user: any): Promise<{
        id: any;
        email: any;
        name: any;
        role: any;
        avatar: any;
        phone: any;
        defaultAddress: any;
    }>;
    updateProfile(user: any, updateData: Partial<User>): Promise<{
        id: unknown;
        email: string;
        name: string;
        role: string;
        avatar: string | undefined;
        phone: string | undefined;
        defaultAddress: {
            firstName: string;
            lastName: string;
            address: string;
            city: string;
            state: string;
            zipCode: string;
            country: string;
        } | undefined;
    }>;
}
