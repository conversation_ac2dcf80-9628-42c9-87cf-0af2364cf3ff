import { JwtService } from '@nestjs/jwt';
import { Model } from 'mongoose';
import { User, UserDocument } from '../schemas/user.schema';
import { CreateUserDto } from './dto/create-user.dto';
import { LoginDto } from './dto/login.dto';
export declare class AuthService {
    private userModel;
    private jwtService;
    constructor(userModel: Model<UserDocument>, jwtService: JwtService);
    validateUser(email: string, password: string): Promise<any>;
    login(loginDto: LoginDto): Promise<{
        access_token: string;
        user: {
            id: any;
            email: any;
            name: any;
            role: any;
            avatar: any;
        };
    }>;
    register(createUserDto: CreateUserDto): Promise<{
        access_token: string;
        user: {
            id: unknown;
            email: string;
            name: string;
            role: string;
            avatar: string | undefined;
        };
    }>;
    googleLogin(req: any): Promise<{
        access_token: string;
        user: {
            id: unknown;
            email: string;
            name: string;
            role: string;
            avatar: string | undefined;
        };
    }>;
    findById(id: string): Promise<UserDocument | null>;
    updateProfile(userId: string, updateData: Partial<User>): Promise<UserDocument | null>;
}
