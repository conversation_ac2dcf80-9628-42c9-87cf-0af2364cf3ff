"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const product_schema_1 = require("../schemas/product.schema");
const review_schema_1 = require("../schemas/review.schema");
let ProductsService = class ProductsService {
    productModel;
    reviewModel;
    constructor(productModel, reviewModel) {
        this.productModel = productModel;
        this.reviewModel = reviewModel;
    }
    async create(createProductDto, userId) {
        const product = new this.productModel({
            ...createProductDto,
            createdBy: userId,
        });
        return product.save();
    }
    async findAll(filters) {
        const { page = 1, limit = 12, category, size, bedazzlingLevel, frameOption, priceRange, rating, inStock, featured, sortBy = 'createdAt', sortOrder = 'desc', search, } = filters;
        const query = { isActive: true };
        if (category)
            query.category = category;
        if (featured !== undefined)
            query.featured = featured;
        if (rating)
            query.rating = { $gte: rating };
        if (inStock)
            query.totalStock = { $gt: 0 };
        if (priceRange && priceRange.length === 2) {
            query.basePrice = { $gte: priceRange[0], $lte: priceRange[1] };
        }
        if (size && size.length > 0) {
            query['variants.size'] = { $in: size };
        }
        if (bedazzlingLevel && bedazzlingLevel.length > 0) {
            query['variants.bedazzlingLevel'] = { $in: bedazzlingLevel };
        }
        if (frameOption && frameOption.length > 0) {
            query['variants.frameOption'] = { $in: frameOption };
        }
        if (search) {
            query.$text = { $search: search };
        }
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const skip = (page - 1) * limit;
        const [products, total] = await Promise.all([
            this.productModel
                .find(query)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .populate('createdBy', 'name')
                .exec(),
            this.productModel.countDocuments(query),
        ]);
        return {
            data: products,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
            },
        };
    }
    async findOne(id) {
        const product = await this.productModel
            .findById(id)
            .populate('createdBy', 'name')
            .exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return product;
    }
    async update(id, updateProductDto) {
        const product = await this.productModel
            .findByIdAndUpdate(id, updateProductDto, { new: true })
            .exec();
        if (!product) {
            throw new common_1.NotFoundException('Product not found');
        }
        return product;
    }
    async remove(id) {
        const result = await this.productModel.findByIdAndDelete(id).exec();
        if (!result) {
            throw new common_1.NotFoundException('Product not found');
        }
    }
    async getFeatured(limit = 8) {
        return this.productModel
            .find({ featured: true, isActive: true })
            .sort({ createdAt: -1 })
            .limit(limit)
            .exec();
    }
    async getRelated(productId, limit = 4) {
        const product = await this.findOne(productId);
        return this.productModel
            .find({
            _id: { $ne: productId },
            category: product.category,
            isActive: true,
        })
            .sort({ rating: -1 })
            .limit(limit)
            .exec();
    }
    async updateRating(productId) {
        const reviews = await this.reviewModel
            .find({ productId, isActive: true })
            .exec();
        if (reviews.length === 0) {
            await this.productModel.findByIdAndUpdate(productId, {
                rating: 0,
                reviewCount: 0,
            });
            return;
        }
        const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
        const averageRating = totalRating / reviews.length;
        await this.productModel.findByIdAndUpdate(productId, {
            rating: Math.round(averageRating * 10) / 10,
            reviewCount: reviews.length,
        });
    }
    async updateStock(productId, variantId, quantity) {
        const product = await this.findOne(productId);
        const variant = product.variants.find(v => v.id === variantId);
        if (!variant) {
            throw new common_1.BadRequestException('Variant not found');
        }
        if (variant.stock < quantity) {
            throw new common_1.BadRequestException('Insufficient stock');
        }
        await this.productModel.updateOne({ _id: productId, 'variants.id': variantId }, { $inc: { 'variants.$.stock': -quantity } });
        const updatedProduct = await this.findOne(productId);
        const totalStock = updatedProduct.variants.reduce((sum, v) => sum + v.stock, 0);
        await this.productModel.findByIdAndUpdate(productId, { totalStock });
    }
    async searchProducts(query, limit = 10) {
        return this.productModel
            .find({
            $text: { $search: query },
            isActive: true,
        })
            .sort({ score: { $meta: 'textScore' } })
            .limit(limit)
            .exec();
    }
    async getProductsByCategory(category, limit = 12) {
        return this.productModel
            .find({ category, isActive: true })
            .sort({ createdAt: -1 })
            .limit(limit)
            .exec();
    }
};
exports.ProductsService = ProductsService;
exports.ProductsService = ProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(product_schema_1.Product.name)),
    __param(1, (0, mongoose_1.InjectModel)(review_schema_1.Review.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], ProductsService);
//# sourceMappingURL=products.service.js.map