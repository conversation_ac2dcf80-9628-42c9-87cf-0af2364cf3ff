declare class ProductVariantDto {
    id: string;
    size: string;
    bedazzlingLevel: string;
    frameOption: string;
    complexity?: string;
    price: number;
    stock: number;
}
export declare class CreateProductDto {
    name: string;
    description: string;
    category: string;
    images?: string[];
    variants: ProductVariantDto[];
    basePrice: number;
    featured?: boolean;
    tags?: string[];
    sku?: string;
    metaTitle?: string;
    metaDescription?: string;
}
export {};
