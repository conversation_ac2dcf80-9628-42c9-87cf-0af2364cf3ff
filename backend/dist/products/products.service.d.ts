import { Model } from 'mongoose';
import { Product, ProductDocument } from '../schemas/product.schema';
import { ReviewDocument } from '../schemas/review.schema';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductFiltersDto } from './dto/product-filters.dto';
export declare class ProductsService {
    private productModel;
    private reviewModel;
    constructor(productModel: Model<ProductDocument>, reviewModel: Model<ReviewDocument>);
    create(createProductDto: CreateProductDto, userId: string): Promise<ProductDocument>;
    findAll(filters: ProductFiltersDto): Promise<{
        data: (import("mongoose").Document<unknown, {}, ProductDocument, {}> & Product & import("mongoose").Document<unknown, any, any, Record<string, any>> & Required<{
            _id: unknown;
        }> & {
            __v: number;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    findOne(id: string): Promise<ProductDocument>;
    update(id: string, updateProductDto: UpdateProductDto): Promise<ProductDocument>;
    remove(id: string): Promise<void>;
    getFeatured(limit?: number): Promise<ProductDocument[]>;
    getRelated(productId: string, limit?: number): Promise<ProductDocument[]>;
    updateRating(productId: string): Promise<void>;
    updateStock(productId: string, variantId: string, quantity: number): Promise<void>;
    searchProducts(query: string, limit?: number): Promise<ProductDocument[]>;
    getProductsByCategory(category: string, limit?: number): Promise<ProductDocument[]>;
}
