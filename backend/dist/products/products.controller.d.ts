import { ProductsService } from './products.service';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { ProductFiltersDto } from './dto/product-filters.dto';
export declare class ProductsController {
    private readonly productsService;
    constructor(productsService: ProductsService);
    create(createProductDto: CreateProductDto, user: any, files: Express.Multer.File[]): Promise<import("../schemas/product.schema").ProductDocument>;
    findAll(filters: ProductFiltersDto): Promise<{
        data: (import("mongoose").Document<unknown, {}, import("../schemas/product.schema").ProductDocument, {}> & import("../schemas/product.schema").Product & import("mongoose").Document<unknown, any, any, Record<string, any>> & Required<{
            _id: unknown;
        }> & {
            __v: number;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
        };
    }>;
    getFeatured(limit?: number): Promise<import("../schemas/product.schema").ProductDocument[]>;
    search(query: string, limit?: number): Promise<import("../schemas/product.schema").ProductDocument[]>;
    getByCategory(category: string, limit?: number): Promise<import("../schemas/product.schema").ProductDocument[]>;
    findOne(id: string): Promise<import("../schemas/product.schema").ProductDocument>;
    getRelated(id: string, limit?: number): Promise<import("../schemas/product.schema").ProductDocument[]>;
    update(id: string, updateProductDto: UpdateProductDto, files: Express.Multer.File[]): Promise<import("../schemas/product.schema").ProductDocument>;
    remove(id: string): Promise<void>;
}
