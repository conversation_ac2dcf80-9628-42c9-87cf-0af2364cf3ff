import { Document, Types } from 'mongoose';
export type ProductDocument = Product & Document;
export declare class ProductVariant {
    id: string;
    size: string;
    bedazzlingLevel: string;
    frameOption: string;
    complexity?: string;
    price: number;
    stock: number;
}
export declare class Product {
    name: string;
    description: string;
    category: string;
    images: string[];
    variants: ProductVariant[];
    basePrice: number;
    featured: boolean;
    tags: string[];
    rating: number;
    reviewCount: number;
    isActive: boolean;
    sku?: string;
    totalStock: number;
    soldCount: number;
    metaTitle?: string;
    metaDescription?: string;
    createdBy: Types.ObjectId;
}
export declare const ProductSchema: import("mongoose").Schema<Product, import("mongoose").Model<Product, any, any, any, Document<unknown, any, Product, any> & Product & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Product, Document<unknown, {}, import("mongoose").FlatRecord<Product>, {}> & import("mongoose").FlatRecord<Product> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
