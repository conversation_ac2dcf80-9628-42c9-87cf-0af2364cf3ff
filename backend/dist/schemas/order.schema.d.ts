import { Document, Types } from 'mongoose';
export type OrderDocument = Order & Document;
export declare class ShippingAddress {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
}
export declare class OrderItem {
    productId: string;
    variantId: string;
    quantity: number;
    price: number;
    productName: string;
    productImage?: string;
    variantDetails: {
        size: string;
        bedazzlingLevel: string;
        frameOption: string;
        complexity?: string;
    };
}
export declare class Order {
    userId: Types.ObjectId;
    orderNumber: string;
    items: OrderItem[];
    subtotal: number;
    shipping: number;
    tax: number;
    total: number;
    status: string;
    shippingAddress: ShippingAddress;
    notes?: string;
    trackingNumber?: string;
    estimatedDelivery?: Date;
    deliveredAt?: Date;
    cancelledAt?: Date;
    cancellationReason?: string;
    statusHistory: string[];
}
export declare const OrderSchema: import("mongoose").Schema<Order, import("mongoose").Model<Order, any, any, any, Document<unknown, any, Order, any> & Order & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Order, Document<unknown, {}, import("mongoose").FlatRecord<Order>, {}> & import("mongoose").FlatRecord<Order> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export declare class CustomOrderRequest {
    userId: Types.ObjectId;
    customerName: string;
    email: string;
    phone: string;
    description: string;
    referenceImages: string[];
    preferredSize: string;
    preferredBedazzling: string;
    preferredFrame: string;
    budget: number;
    deadline?: Date;
    status: string;
    quote?: number;
    notes?: string;
    adminNotes?: string;
}
export type CustomOrderRequestDocument = CustomOrderRequest & Document;
export declare const CustomOrderRequestSchema: import("mongoose").Schema<CustomOrderRequest, import("mongoose").Model<CustomOrderRequest, any, any, any, Document<unknown, any, CustomOrderRequest, any> & CustomOrderRequest & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, CustomOrderRequest, Document<unknown, {}, import("mongoose").FlatRecord<CustomOrderRequest>, {}> & import("mongoose").FlatRecord<CustomOrderRequest> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
