{"version": 3, "file": "product.schema.js", "sourceRoot": "", "sources": ["../../src/schemas/product.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AAC/D,uCAA2C;AAKpC,IAAM,cAAc,GAApB,MAAM,cAAc;IAEzB,EAAE,CAAS;IAGX,IAAI,CAAS;IAGb,eAAe,CAAS;IAGxB,WAAW,CAAS;IAGpB,UAAU,CAAU;IAGpB,KAAK,CAAS;IAGd,KAAK,CAAS;CACf,CAAA;AArBY,wCAAc;AAEzB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;0CACd;AAGX;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CAC/D;AAGb;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;uDAChD;AAGxB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;mDAC3C;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,EAAE,CAAC;;kDAChC;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;;6CACnB;AAGd;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;6CAC/B;yBApBH,cAAc;IAD1B,IAAA,iBAAM,EAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;GACV,cAAc,CAqB1B;AAGM,IAAM,OAAO,GAAb,MAAM,OAAO;IAElB,IAAI,CAAS;IAGb,WAAW,CAAS;IAGpB,QAAQ,CAAS;IAGjB,MAAM,CAAW;IAGjB,QAAQ,CAAmB;IAG3B,SAAS,CAAS;IAGlB,QAAQ,CAAU;IAGlB,IAAI,CAAW;IAGf,MAAM,CAAS;IAGf,WAAW,CAAS;IAGpB,QAAQ,CAAU;IAGlB,GAAG,CAAU;IAGb,UAAU,CAAS;IAGnB,SAAS,CAAS;IAGlB,SAAS,CAAU;IAGnB,eAAe,CAAU;IAGzB,SAAS,CAAiB;CAC3B,CAAA;AAnDY,0BAAO;AAElB;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;qCACZ;AAGb;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;4CACL;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;;yCACtC;AAGjB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;uCACrB;AAGjB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;yCACnB;AAG3B;IADC,IAAA,eAAI,EAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;;0CACf;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;;yCACP;AAGlB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;;qCACvB;AAGf;IADC,IAAA,eAAI,EAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;uCACtB;AAGf;IADC,IAAA,eAAI,EAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;4CACT;AAGpB;IADC,IAAA,eAAI,EAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;;yCACN;AAGlB;IADC,IAAA,eAAI,GAAE;;oCACM;AAGb;IADC,IAAA,eAAI,EAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;2CACV;AAGnB;IADC,IAAA,eAAI,EAAC,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;;0CACX;AAGlB;IADC,IAAA,eAAI,GAAE;;0CACY;AAGnB;IADC,IAAA,eAAI,GAAE;;gDACkB;AAGzB;IADC,IAAA,eAAI,EAAC,EAAE,IAAI,EAAE,gBAAK,CAAC,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;8BACjC,gBAAK,CAAC,QAAQ;0CAAC;kBAlDf,OAAO;IADnB,IAAA,iBAAM,EAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;GAChB,OAAO,CAmDnB;AAEY,QAAA,aAAa,GAAG,wBAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AAGnE,qBAAa,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;AACzE,qBAAa,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AACrC,qBAAa,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AACrC,qBAAa,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACpC,qBAAa,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACvC,qBAAa,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AACtC,qBAAa,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AACrC,qBAAa,CAAC,KAAK,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,CAAC,CAAC;AAC5C,qBAAa,CAAC,KAAK,CAAC,EAAE,0BAA0B,EAAE,CAAC,EAAE,CAAC,CAAC;AACvD,qBAAa,CAAC,KAAK,CAAC,EAAE,sBAAsB,EAAE,CAAC,EAAE,CAAC,CAAC"}