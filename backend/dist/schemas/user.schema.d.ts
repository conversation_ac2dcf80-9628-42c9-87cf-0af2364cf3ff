import { Document } from 'mongoose';
export type UserDocument = User & Document;
export declare class User {
    email: string;
    name: string;
    avatar?: string;
    role: string;
    googleId?: string;
    password?: string;
    isActive: boolean;
    lastLoginAt?: Date;
    phone?: string;
    defaultAddress?: {
        firstName: string;
        lastName: string;
        address: string;
        city: string;
        state: string;
        zipCode: string;
        country: string;
    };
}
export declare const UserSchema: import("mongoose").Schema<User, import("mongoose").Model<User, any, any, any, Document<unknown, any, User, any> & User & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, User, Document<unknown, {}, import("mongoose").FlatRecord<User>, {}> & import("mongoose").FlatRecord<User> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
