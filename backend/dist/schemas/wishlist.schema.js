"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WishlistSchema = exports.Wishlist = exports.WishlistItem = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let WishlistItem = class WishlistItem {
    productId;
    variantId;
    addedAt;
};
exports.WishlistItem = WishlistItem;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'Product', required: true }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], WishlistItem.prototype, "productId", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], WishlistItem.prototype, "variantId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    __metadata("design:type", Date)
], WishlistItem.prototype, "addedAt", void 0);
exports.WishlistItem = WishlistItem = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], WishlistItem);
let Wishlist = class Wishlist {
    userId;
    items;
};
exports.Wishlist = Wishlist;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'User', required: true, unique: true }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Wishlist.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [WishlistItem], default: [] }),
    __metadata("design:type", Array)
], Wishlist.prototype, "items", void 0);
exports.Wishlist = Wishlist = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Wishlist);
exports.WishlistSchema = mongoose_1.SchemaFactory.createForClass(Wishlist);
exports.WishlistSchema.index({ userId: 1 });
exports.WishlistSchema.index({ 'items.productId': 1 });
exports.WishlistSchema.index({ 'items.addedAt': -1 });
//# sourceMappingURL=wishlist.schema.js.map