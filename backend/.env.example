# Database
MONGODB_URI=mongodb+srv://connect:<EMAIL>/?retryWrites=true&w=majority&appName=dazzled

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-in-production-make-it-very-long-and-random
JWT_EXPIRES_IN=100d

# Google OAuth
GOOGLE_CLIENT_ID=831471427369-hcdkbtnervmo3pcr0gka8elaqd8nqg2k.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Application
PORT=3001
NODE_ENV=development

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads

# Email Configuration (SMTP)
# For Gmail: Use App Password, not regular password
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_FROM_NAME=Bedazzled
SMTP_PASS=mpmp vqgy timx iqks
SMTP_FROM=<EMAIL>