{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/components/ui/card.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border border-border bg-card text-card-foreground shadow-sm',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('text-2xl font-semibold leading-none tracking-tight', className)}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p ref={ref} className={cn('text-sm text-muted-foreground', className)} {...props} />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('flex items-center p-6 pt-0', className)} {...props} />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAO,8TAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8VAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,8TAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8VAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,8TAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8VAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,8TAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8VAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;;;AAEnF,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,8TAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8VAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8TAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8VAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/components/products/product-card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { motion } from 'framer-motion';\nimport { \n  HeartIcon, \n  StarIcon, \n  ShoppingCartIcon,\n  EyeIcon\n} from '@heroicons/react/24/outline';\nimport { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { formatPrice } from '@/lib/utils';\nimport { Product } from '@/types';\n\ninterface ProductCardProps {\n  product: Product;\n  isWishlisted?: boolean;\n  onAddToWishlist?: (productId: string) => void;\n  onAddToCart?: (productId: string, variantId: string) => void;\n  className?: string;\n}\n\nexport function ProductCard({ \n  product, \n  isWishlisted = false, \n  onAddToWishlist, \n  onAddToCart,\n  className \n}: ProductCardProps) {\n  const defaultVariant = product.variants[0];\n  const discountPercentage = product.featured ? 15 : 0;\n  const discountedPrice = discountPercentage > 0 \n    ? defaultVariant.price * (1 - discountPercentage / 100)\n    : defaultVariant.price;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      whileHover={{ y: -5 }}\n      transition={{ duration: 0.3 }}\n      className={className}\n    >\n      <Card className=\"group overflow-hidden border-0 shadow-md hover:shadow-xl transition-all duration-300\">\n        <div className=\"relative aspect-square overflow-hidden bg-muted\">\n          {/* Product Image */}\n          <Link href={`/products/${product.id}`}>\n            <div className=\"relative w-full h-full\">\n              {product.images.length > 0 ? (\n                <Image\n                  src={product.images[0]}\n                  alt={product.name}\n                  fill\n                  className=\"object-cover transition-transform duration-300 group-hover:scale-105\"\n                />\n              ) : (\n                <div className=\"w-full h-full bg-gradient-to-br from-accent/20 to-highlight/20 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"w-16 h-16 mx-auto bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center mb-2\">\n                      <span className=\"text-white font-bold text-lg\">B</span>\n                    </div>\n                    <p className=\"text-sm text-muted-foreground\">No Image</p>\n                  </div>\n                </div>\n              )}\n            </div>\n          </Link>\n\n          {/* Badges */}\n          <div className=\"absolute top-3 left-3 flex flex-col gap-2\">\n            {product.featured && (\n              <Badge variant=\"destructive\" className=\"text-xs\">\n                Featured\n              </Badge>\n            )}\n            {discountPercentage > 0 && (\n              <Badge variant=\"success\" className=\"text-xs\">\n                -{discountPercentage}%\n              </Badge>\n            )}\n            {product.category === 'custom' && (\n              <Badge variant=\"secondary\" className=\"text-xs\">\n                Custom\n              </Badge>\n            )}\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n            <Button\n              size=\"icon\"\n              variant=\"outline\"\n              className=\"h-8 w-8 bg-white/90 hover:bg-white\"\n              onClick={() => onAddToWishlist?.(product.id)}\n            >\n              {isWishlisted ? (\n                <HeartSolidIcon className=\"h-4 w-4 text-red-500\" />\n              ) : (\n                <HeartIcon className=\"h-4 w-4\" />\n              )}\n            </Button>\n            <Link href={`/products/${product.id}`}>\n              <Button\n                size=\"icon\"\n                variant=\"outline\"\n                className=\"h-8 w-8 bg-white/90 hover:bg-white\"\n              >\n                <EyeIcon className=\"h-4 w-4\" />\n              </Button>\n            </Link>\n          </div>\n\n          {/* Quick Add to Cart */}\n          <div className=\"absolute bottom-3 left-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n            <Button\n              size=\"sm\"\n              className=\"w-full\"\n              onClick={() => onAddToCart?.(product.id, defaultVariant.id)}\n            >\n              <ShoppingCartIcon className=\"h-4 w-4 mr-2\" />\n              Quick Add\n            </Button>\n          </div>\n        </div>\n\n        <CardContent className=\"p-4\">\n          <div className=\"space-y-2\">\n            {/* Product Name */}\n            <Link href={`/products/${product.id}`}>\n              <h3 className=\"font-semibold text-foreground hover:text-accent transition-colors line-clamp-2\">\n                {product.name}\n              </h3>\n            </Link>\n\n            {/* Rating */}\n            <div className=\"flex items-center gap-1\">\n              <div className=\"flex items-center\">\n                {[...Array(5)].map((_, i) => (\n                  <StarIcon\n                    key={i}\n                    className={`h-4 w-4 ${\n                      i < Math.floor(product.rating)\n                        ? 'text-warning fill-current'\n                        : 'text-muted-foreground'\n                    }`}\n                  />\n                ))}\n              </div>\n              <span className=\"text-sm text-muted-foreground\">\n                ({product.reviewCount})\n              </span>\n            </div>\n\n            {/* Price */}\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-lg font-bold text-foreground\">\n                {formatPrice(discountedPrice)}\n              </span>\n              {discountPercentage > 0 && (\n                <span className=\"text-sm text-muted-foreground line-through\">\n                  {formatPrice(defaultVariant.price)}\n                </span>\n              )}\n            </div>\n\n            {/* Variant Info */}\n            <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\n              <span className=\"capitalize\">{defaultVariant.size}</span>\n              <span>•</span>\n              <span className=\"capitalize\">{defaultVariant.bedazzlingLevel}</span>\n              <span>•</span>\n              <span className=\"capitalize\">{defaultVariant.frameOption}</span>\n            </div>\n\n            {/* Stock Status */}\n            {defaultVariant.stock > 0 ? (\n              <Badge variant=\"success\" className=\"text-xs w-fit\">\n                In Stock\n              </Badge>\n            ) : (\n              <Badge variant=\"destructive\" className=\"text-xs w-fit\">\n                Out of Stock\n              </Badge>\n            )}\n          </div>\n        </CardContent>\n      </Card>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;AA2BO,SAAS,YAAY,KAMT;QANS,EAC1B,OAAO,EACP,eAAe,KAAK,EACpB,eAAe,EACf,WAAW,EACX,SAAS,EACQ,GANS;IAO1B,MAAM,iBAAiB,QAAQ,QAAQ,CAAC,EAAE;IAC1C,MAAM,qBAAqB,QAAQ,QAAQ,GAAG,KAAK;IACnD,MAAM,kBAAkB,qBAAqB,IACzC,eAAe,KAAK,GAAG,CAAC,IAAI,qBAAqB,GAAG,IACpD,eAAe,KAAK;IAExB,qBACE,8VAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW;kBAEX,cAAA,8VAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8VAAC;oBAAI,WAAU;;sCAEb,8VAAC,gUAAA,CAAA,UAAI;4BAAC,MAAM,AAAC,aAAuB,OAAX,QAAQ,EAAE;sCACjC,cAAA,8VAAC;gCAAI,WAAU;0CACZ,QAAQ,MAAM,CAAC,MAAM,GAAG,kBACvB,8VAAC,iSAAA,CAAA,UAAK;oCACJ,KAAK,QAAQ,MAAM,CAAC,EAAE;oCACtB,KAAK,QAAQ,IAAI;oCACjB,IAAI;oCACJ,WAAU;;;;;yDAGZ,8VAAC;oCAAI,WAAU;8CACb,cAAA,8VAAC;wCAAI,WAAU;;0DACb,8VAAC;gDAAI,WAAU;0DACb,cAAA,8VAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8VAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvD,8VAAC;4BAAI,WAAU;;gCACZ,QAAQ,QAAQ,kBACf,8VAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAc,WAAU;8CAAU;;;;;;gCAIlD,qBAAqB,mBACpB,8VAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAAU;wCACzC;wCAAmB;;;;;;;gCAGxB,QAAQ,QAAQ,KAAK,0BACpB,8VAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAU;;;;;;;;;;;;sCAOnD,8VAAC;4BAAI,WAAU;;8CACb,8VAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS,IAAM,4BAAA,sCAAA,gBAAkB,QAAQ,EAAE;8CAE1C,6BACC,8VAAC,ySAAA,CAAA,YAAc;wCAAC,WAAU;;;;;6DAE1B,8VAAC,2SAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAGzB,8VAAC,gUAAA,CAAA,UAAI;oCAAC,MAAM,AAAC,aAAuB,OAAX,QAAQ,EAAE;8CACjC,cAAA,8VAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;kDAEV,cAAA,8VAAC,uSAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAMzB,8VAAC;4BAAI,WAAU;sCACb,cAAA,8VAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,wBAAA,kCAAA,YAAc,QAAQ,EAAE,EAAE,eAAe,EAAE;;kDAE1D,8VAAC,yTAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAMnD,8VAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8VAAC;wBAAI,WAAU;;0CAEb,8VAAC,gUAAA,CAAA,UAAI;gCAAC,MAAM,AAAC,aAAuB,OAAX,QAAQ,EAAE;0CACjC,cAAA,8VAAC;oCAAG,WAAU;8CACX,QAAQ,IAAI;;;;;;;;;;;0CAKjB,8VAAC;gCAAI,WAAU;;kDACb,8VAAC;wCAAI,WAAU;kDACZ;+CAAI,MAAM;yCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8VAAC,ySAAA,CAAA,WAAQ;gDAEP,WAAW,AAAC,WAIX,OAHC,IAAI,KAAK,KAAK,CAAC,QAAQ,MAAM,IACzB,8BACA;+CAJD;;;;;;;;;;kDASX,8VAAC;wCAAK,WAAU;;4CAAgC;4CAC5C,QAAQ,WAAW;4CAAC;;;;;;;;;;;;;0CAK1B,8VAAC;gCAAI,WAAU;;kDACb,8VAAC;wCAAK,WAAU;kDACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;oCAEd,qBAAqB,mBACpB,8VAAC;wCAAK,WAAU;kDACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,eAAe,KAAK;;;;;;;;;;;;0CAMvC,8VAAC;gCAAI,WAAU;;kDACb,8VAAC;wCAAK,WAAU;kDAAc,eAAe,IAAI;;;;;;kDACjD,8VAAC;kDAAK;;;;;;kDACN,8VAAC;wCAAK,WAAU;kDAAc,eAAe,eAAe;;;;;;kDAC5D,8VAAC;kDAAK;;;;;;kDACN,8VAAC;wCAAK,WAAU;kDAAc,eAAe,WAAW;;;;;;;;;;;;4BAIzD,eAAe,KAAK,GAAG,kBACtB,8VAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;0CAAgB;;;;;qDAInD,8VAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAc,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrE;KAvKgB", "debugId": null}}, {"offset": {"line": 553, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/components/products/product-grid.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { ProductCard } from './product-card';\nimport { Product } from '@/types';\n\ninterface ProductGridProps {\n  products: Product[];\n  isLoading?: boolean;\n  wishlistedProducts?: string[];\n  onAddToWishlist?: (productId: string) => void;\n  onAddToCart?: (productId: string, variantId: string) => void;\n}\n\nexport function ProductGrid({ \n  products, \n  isLoading = false,\n  wishlistedProducts = [],\n  onAddToWishlist,\n  onAddToCart \n}: ProductGridProps) {\n  if (isLoading) {\n    return (\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {[...Array(8)].map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"aspect-square bg-muted rounded-lg mb-4\"></div>\n            <div className=\"space-y-2\">\n              <div className=\"h-4 bg-muted rounded w-3/4\"></div>\n              <div className=\"h-3 bg-muted rounded w-1/2\"></div>\n              <div className=\"h-4 bg-muted rounded w-1/4\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (products.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-accent/20 to-highlight/20 rounded-full flex items-center justify-center\">\n          <div className=\"w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center\">\n            <span className=\"text-white font-bold\">B</span>\n          </div>\n        </div>\n        <h3 className=\"text-lg font-semibold text-foreground mb-2\">No Products Found</h3>\n        <p className=\"text-muted-foreground\">\n          Try adjusting your filters or search terms to find what you're looking for.\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n      {products.map((product, index) => (\n        <motion.div\n          key={product.id}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3, delay: index * 0.1 }}\n        >\n          <ProductCard\n            product={product}\n            isWishlisted={wishlistedProducts.includes(product.id)}\n            onAddToWishlist={onAddToWishlist}\n            onAddToCart={onAddToCart}\n          />\n        </motion.div>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAeO,SAAS,YAAY,KAMT;QANS,EAC1B,QAAQ,EACR,YAAY,KAAK,EACjB,qBAAqB,EAAE,EACvB,eAAe,EACf,WAAW,EACM,GANS;IAO1B,IAAI,WAAW;QACb,qBACE,8VAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8VAAC;oBAAY,WAAU;;sCACrB,8VAAC;4BAAI,WAAU;;;;;;sCACf,8VAAC;4BAAI,WAAU;;8CACb,8VAAC;oCAAI,WAAU;;;;;;8CACf,8VAAC;oCAAI,WAAU;;;;;;8CACf,8VAAC;oCAAI,WAAU;;;;;;;;;;;;;mBALT;;;;;;;;;;IAWlB;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,8VAAC;YAAI,WAAU;;8BACb,8VAAC;oBAAI,WAAU;8BACb,cAAA,8VAAC;wBAAI,WAAU;kCACb,cAAA,8VAAC;4BAAK,WAAU;sCAAuB;;;;;;;;;;;;;;;;8BAG3C,8VAAC;oBAAG,WAAU;8BAA6C;;;;;;8BAC3D,8VAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAK3C;IAEA,qBACE,8VAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8VAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;0BAEhD,cAAA,8VAAC,oJAAA,CAAA,cAAW;oBACV,SAAS;oBACT,cAAc,mBAAmB,QAAQ,CAAC,QAAQ,EAAE;oBACpD,iBAAiB;oBACjB,aAAa;;;;;;eATV,QAAQ,EAAE;;;;;;;;;;AAezB;KA3DgB", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/components/products/product-filters.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  FunnelIcon, \n  XMarkIcon,\n  ChevronDownIcon,\n  ChevronUpIcon\n} from '@heroicons/react/24/outline';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { ProductFilters as FilterType } from '@/types';\n\ninterface ProductFiltersProps {\n  filters: FilterType;\n  onFiltersChange: (filters: FilterType) => void;\n  onClearFilters: () => void;\n  isOpen: boolean;\n  onToggle: () => void;\n}\n\nexport function ProductFilters({ \n  filters, \n  onFiltersChange, \n  onClearFilters,\n  isOpen,\n  onToggle \n}: ProductFiltersProps) {\n  const [expandedSections, setExpandedSections] = useState<string[]>([\n    'category', 'price', 'size', 'bedazzling'\n  ]);\n\n  const toggleSection = (section: string) => {\n    setExpandedSections(prev => \n      prev.includes(section) \n        ? prev.filter(s => s !== section)\n        : [...prev, section]\n    );\n  };\n\n  const updateFilter = (key: keyof FilterType, value: any) => {\n    onFiltersChange({ ...filters, [key]: value });\n  };\n\n  const toggleArrayFilter = (key: keyof FilterType, value: string) => {\n    const currentArray = (filters[key] as string[]) || [];\n    const newArray = currentArray.includes(value)\n      ? currentArray.filter(item => item !== value)\n      : [...currentArray, value];\n    updateFilter(key, newArray);\n  };\n\n  const getActiveFiltersCount = () => {\n    let count = 0;\n    if (filters.category) count++;\n    if (filters.size?.length) count += filters.size.length;\n    if (filters.bedazzlingLevel?.length) count += filters.bedazzlingLevel.length;\n    if (filters.frameOption?.length) count += filters.frameOption.length;\n    if (filters.priceRange) count++;\n    if (filters.rating) count++;\n    if (filters.inStock) count++;\n    if (filters.featured) count++;\n    return count;\n  };\n\n  const FilterSection = ({ \n    title, \n    sectionKey, \n    children \n  }: { \n    title: string; \n    sectionKey: string; \n    children: React.ReactNode; \n  }) => (\n    <div className=\"border-b border-border last:border-b-0\">\n      <button\n        onClick={() => toggleSection(sectionKey)}\n        className=\"flex items-center justify-between w-full py-4 text-left hover:text-accent transition-colors\"\n      >\n        <span className=\"font-medium\">{title}</span>\n        {expandedSections.includes(sectionKey) ? (\n          <ChevronUpIcon className=\"h-4 w-4\" />\n        ) : (\n          <ChevronDownIcon className=\"h-4 w-4\" />\n        )}\n      </button>\n      <AnimatePresence>\n        {expandedSections.includes(sectionKey) && (\n          <motion.div\n            initial={{ height: 0, opacity: 0 }}\n            animate={{ height: 'auto', opacity: 1 }}\n            exit={{ height: 0, opacity: 0 }}\n            transition={{ duration: 0.2 }}\n            className=\"overflow-hidden pb-4\"\n          >\n            {children}\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n\n  const CheckboxOption = ({ \n    label, \n    checked, \n    onChange \n  }: { \n    label: string; \n    checked: boolean; \n    onChange: () => void; \n  }) => (\n    <label className=\"flex items-center space-x-2 cursor-pointer hover:text-accent transition-colors\">\n      <input\n        type=\"checkbox\"\n        checked={checked}\n        onChange={onChange}\n        className=\"rounded border-border text-accent focus:ring-accent\"\n      />\n      <span className=\"text-sm capitalize\">{label}</span>\n    </label>\n  );\n\n  return (\n    <>\n      {/* Mobile Filter Toggle */}\n      <div className=\"lg:hidden mb-6\">\n        <Button\n          variant=\"outline\"\n          onClick={onToggle}\n          className=\"w-full justify-between\"\n        >\n          <div className=\"flex items-center\">\n            <FunnelIcon className=\"h-4 w-4 mr-2\" />\n            Filters\n            {getActiveFiltersCount() > 0 && (\n              <Badge variant=\"secondary\" className=\"ml-2\">\n                {getActiveFiltersCount()}\n              </Badge>\n            )}\n          </div>\n          {isOpen ? (\n            <ChevronUpIcon className=\"h-4 w-4\" />\n          ) : (\n            <ChevronDownIcon className=\"h-4 w-4\" />\n          )}\n        </Button>\n      </div>\n\n      {/* Filter Panel */}\n      <AnimatePresence>\n        {(isOpen || window.innerWidth >= 1024) && (\n          <motion.div\n            initial={{ height: 0, opacity: 0 }}\n            animate={{ height: 'auto', opacity: 1 }}\n            exit={{ height: 0, opacity: 0 }}\n            className=\"lg:block\"\n          >\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-4\">\n                <CardTitle className=\"text-lg\">Filters</CardTitle>\n                <div className=\"flex items-center gap-2\">\n                  {getActiveFiltersCount() > 0 && (\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={onClearFilters}\n                      className=\"text-xs\"\n                    >\n                      Clear All\n                    </Button>\n                  )}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    onClick={onToggle}\n                    className=\"lg:hidden h-6 w-6\"\n                  >\n                    <XMarkIcon className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </CardHeader>\n              <CardContent className=\"space-y-0\">\n                {/* Category */}\n                <FilterSection title=\"Category\" sectionKey=\"category\">\n                  <div className=\"space-y-2\">\n                    {['pre-made', 'custom'].map(category => (\n                      <CheckboxOption\n                        key={category}\n                        label={category}\n                        checked={filters.category === category}\n                        onChange={() => updateFilter('category', \n                          filters.category === category ? undefined : category\n                        )}\n                      />\n                    ))}\n                  </div>\n                </FilterSection>\n\n                {/* Price Range */}\n                <FilterSection title=\"Price Range\" sectionKey=\"price\">\n                  <div className=\"space-y-3\">\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <Input\n                        type=\"number\"\n                        placeholder=\"Min\"\n                        value={filters.priceRange?.[0] || ''}\n                        onChange={(e) => {\n                          const min = parseInt(e.target.value) || 0;\n                          const max = filters.priceRange?.[1] || 1000;\n                          updateFilter('priceRange', [min, max]);\n                        }}\n                      />\n                      <Input\n                        type=\"number\"\n                        placeholder=\"Max\"\n                        value={filters.priceRange?.[1] || ''}\n                        onChange={(e) => {\n                          const min = filters.priceRange?.[0] || 0;\n                          const max = parseInt(e.target.value) || 1000;\n                          updateFilter('priceRange', [min, max]);\n                        }}\n                      />\n                    </div>\n                  </div>\n                </FilterSection>\n\n                {/* Size */}\n                <FilterSection title=\"Size\" sectionKey=\"size\">\n                  <div className=\"space-y-2\">\n                    {['small', 'medium', 'large', 'extra-large'].map(size => (\n                      <CheckboxOption\n                        key={size}\n                        label={size}\n                        checked={filters.size?.includes(size) || false}\n                        onChange={() => toggleArrayFilter('size', size)}\n                      />\n                    ))}\n                  </div>\n                </FilterSection>\n\n                {/* Bedazzling Level */}\n                <FilterSection title=\"Bedazzling Level\" sectionKey=\"bedazzling\">\n                  <div className=\"space-y-2\">\n                    {['light', 'medium', 'heavy', 'premium'].map(level => (\n                      <CheckboxOption\n                        key={level}\n                        label={level}\n                        checked={filters.bedazzlingLevel?.includes(level) || false}\n                        onChange={() => toggleArrayFilter('bedazzlingLevel', level)}\n                      />\n                    ))}\n                  </div>\n                </FilterSection>\n\n                {/* Frame Option */}\n                <FilterSection title=\"Frame Option\" sectionKey=\"frame\">\n                  <div className=\"space-y-2\">\n                    {['basic', 'premium', 'luxury'].map(frame => (\n                      <CheckboxOption\n                        key={frame}\n                        label={frame}\n                        checked={filters.frameOption?.includes(frame) || false}\n                        onChange={() => toggleArrayFilter('frameOption', frame)}\n                      />\n                    ))}\n                  </div>\n                </FilterSection>\n\n                {/* Other Options */}\n                <FilterSection title=\"Other Options\" sectionKey=\"other\">\n                  <div className=\"space-y-2\">\n                    <CheckboxOption\n                      label=\"In Stock Only\"\n                      checked={filters.inStock || false}\n                      onChange={() => updateFilter('inStock', !filters.inStock)}\n                    />\n                    <CheckboxOption\n                      label=\"Featured Only\"\n                      checked={filters.featured || false}\n                      onChange={() => updateFilter('featured', !filters.featured)}\n                    />\n                  </div>\n                </FilterSection>\n              </CardContent>\n            </Card>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AACA;;;AAbA;;;;;;;;AAwBO,SAAS,eAAe,KAMT;QANS,EAC7B,OAAO,EACP,eAAe,EACf,cAAc,EACd,MAAM,EACN,QAAQ,EACY,GANS;QAwLA,qBAUA;;IA3L7B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8TAAA,CAAA,WAAQ,AAAD,EAAY;QACjE;QAAY;QAAS;QAAQ;KAC9B;IAED,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA,OAClB,KAAK,QAAQ,CAAC,WACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,WACvB;mBAAI;gBAAM;aAAQ;IAE1B;IAEA,MAAM,eAAe,CAAC,KAAuB;QAC3C,gBAAgB;YAAE,GAAG,OAAO;YAAE,CAAC,IAAI,EAAE;QAAM;IAC7C;IAEA,MAAM,oBAAoB,CAAC,KAAuB;QAChD,MAAM,eAAe,AAAC,OAAO,CAAC,IAAI,IAAiB,EAAE;QACrD,MAAM,WAAW,aAAa,QAAQ,CAAC,SACnC,aAAa,MAAM,CAAC,CAAA,OAAQ,SAAS,SACrC;eAAI;YAAc;SAAM;QAC5B,aAAa,KAAK;IACpB;IAEA,MAAM,wBAAwB;YAGxB,eACA,0BACA;QAJJ,IAAI,QAAQ;QACZ,IAAI,QAAQ,QAAQ,EAAE;QACtB,KAAI,gBAAA,QAAQ,IAAI,cAAZ,oCAAA,cAAc,MAAM,EAAE,SAAS,QAAQ,IAAI,CAAC,MAAM;QACtD,KAAI,2BAAA,QAAQ,eAAe,cAAvB,+CAAA,yBAAyB,MAAM,EAAE,SAAS,QAAQ,eAAe,CAAC,MAAM;QAC5E,KAAI,uBAAA,QAAQ,WAAW,cAAnB,2CAAA,qBAAqB,MAAM,EAAE,SAAS,QAAQ,WAAW,CAAC,MAAM;QACpE,IAAI,QAAQ,UAAU,EAAE;QACxB,IAAI,QAAQ,MAAM,EAAE;QACpB,IAAI,QAAQ,OAAO,EAAE;QACrB,IAAI,QAAQ,QAAQ,EAAE;QACtB,OAAO;IACT;IAEA,MAAM,gBAAgB;YAAC,EACrB,KAAK,EACL,UAAU,EACV,QAAQ,EAKT;6BACC,8VAAC;YAAI,WAAU;;8BACb,8VAAC;oBACC,SAAS,IAAM,cAAc;oBAC7B,WAAU;;sCAEV,8VAAC;4BAAK,WAAU;sCAAe;;;;;;wBAC9B,iBAAiB,QAAQ,CAAC,4BACzB,8VAAC,mTAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;iDAEzB,8VAAC,uTAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;;;;;;;8BAG/B,8VAAC,wUAAA,CAAA,kBAAe;8BACb,iBAAiB,QAAQ,CAAC,6BACzB,8VAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBACjC,SAAS;4BAAE,QAAQ;4BAAQ,SAAS;wBAAE;wBACtC,MAAM;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCAET;;;;;;;;;;;;;;;;;;IAOX,MAAM,iBAAiB;YAAC,EACtB,KAAK,EACL,OAAO,EACP,QAAQ,EAKT;6BACC,8VAAC;YAAM,WAAU;;8BACf,8VAAC;oBACC,MAAK;oBACL,SAAS;oBACT,UAAU;oBACV,WAAU;;;;;;8BAEZ,8VAAC;oBAAK,WAAU;8BAAsB;;;;;;;;;;;;;IAI1C,qBACE;;0BAEE,8VAAC;gBAAI,WAAU;0BACb,cAAA,8VAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,SAAS;oBACT,WAAU;;sCAEV,8VAAC;4BAAI,WAAU;;8CACb,8VAAC,6SAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;gCAEtC,0BAA0B,mBACzB,8VAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAClC;;;;;;;;;;;;wBAIN,uBACC,8VAAC,mTAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;iDAEzB,8VAAC,uTAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAMjC,8VAAC,wUAAA,CAAA,kBAAe;0BACb,CAAC,UAAU,OAAO,UAAU,IAAI,IAAI,mBACnC,8VAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBACjC,SAAS;wBAAE,QAAQ;wBAAQ,SAAS;oBAAE;oBACtC,MAAM;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,8VAAC,mIAAA,CAAA,OAAI;;0CACH,8VAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8VAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;kDAC/B,8VAAC;wCAAI,WAAU;;4CACZ,0BAA0B,mBACzB,8VAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;0DACX;;;;;;0DAIH,8VAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;0DAEV,cAAA,8VAAC,2SAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAI3B,8VAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,8VAAC;wCAAc,OAAM;wCAAW,YAAW;kDACzC,cAAA,8VAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAY;6CAAS,CAAC,GAAG,CAAC,CAAA,yBAC1B,8VAAC;oDAEC,OAAO;oDACP,SAAS,QAAQ,QAAQ,KAAK;oDAC9B,UAAU,IAAM,aAAa,YAC3B,QAAQ,QAAQ,KAAK,WAAW,YAAY;mDAJzC;;;;;;;;;;;;;;;kDAYb,8VAAC;wCAAc,OAAM;wCAAc,YAAW;kDAC5C,cAAA,8VAAC;4CAAI,WAAU;sDACb,cAAA,8VAAC;gDAAI,WAAU;;kEACb,8VAAC,oIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,aAAY;wDACZ,OAAO,EAAA,sBAAA,QAAQ,UAAU,cAAlB,0CAAA,mBAAoB,CAAC,EAAE,KAAI;wDAClC,UAAU,CAAC;gEAEG;4DADZ,MAAM,MAAM,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4DACxC,MAAM,MAAM,EAAA,sBAAA,QAAQ,UAAU,cAAlB,0CAAA,mBAAoB,CAAC,EAAE,KAAI;4DACvC,aAAa,cAAc;gEAAC;gEAAK;6DAAI;wDACvC;;;;;;kEAEF,8VAAC,oIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,aAAY;wDACZ,OAAO,EAAA,uBAAA,QAAQ,UAAU,cAAlB,2CAAA,oBAAoB,CAAC,EAAE,KAAI;wDAClC,UAAU,CAAC;gEACG;4DAAZ,MAAM,MAAM,EAAA,sBAAA,QAAQ,UAAU,cAAlB,0CAAA,mBAAoB,CAAC,EAAE,KAAI;4DACvC,MAAM,MAAM,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4DACxC,aAAa,cAAc;gEAAC;gEAAK;6DAAI;wDACvC;;;;;;;;;;;;;;;;;;;;;;kDAOR,8VAAC;wCAAc,OAAM;wCAAO,YAAW;kDACrC,cAAA,8VAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAS;gDAAU;gDAAS;6CAAc,CAAC,GAAG,CAAC,CAAA;oDAIpC;qEAHX,8VAAC;oDAEC,OAAO;oDACP,SAAS,EAAA,gBAAA,QAAQ,IAAI,cAAZ,oCAAA,cAAc,QAAQ,CAAC,UAAS;oDACzC,UAAU,IAAM,kBAAkB,QAAQ;mDAHrC;;;;;;;;;;;;;;;;kDAUb,8VAAC;wCAAc,OAAM;wCAAmB,YAAW;kDACjD,cAAA,8VAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAS;gDAAU;gDAAS;6CAAU,CAAC,GAAG,CAAC,CAAA;oDAIhC;qEAHX,8VAAC;oDAEC,OAAO;oDACP,SAAS,EAAA,2BAAA,QAAQ,eAAe,cAAvB,+CAAA,yBAAyB,QAAQ,CAAC,WAAU;oDACrD,UAAU,IAAM,kBAAkB,mBAAmB;mDAHhD;;;;;;;;;;;;;;;;kDAUb,8VAAC;wCAAc,OAAM;wCAAe,YAAW;kDAC7C,cAAA,8VAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAS;gDAAW;6CAAS,CAAC,GAAG,CAAC,CAAA;oDAIvB;qEAHX,8VAAC;oDAEC,OAAO;oDACP,SAAS,EAAA,uBAAA,QAAQ,WAAW,cAAnB,2CAAA,qBAAqB,QAAQ,CAAC,WAAU;oDACjD,UAAU,IAAM,kBAAkB,eAAe;mDAH5C;;;;;;;;;;;;;;;;kDAUb,8VAAC;wCAAc,OAAM;wCAAgB,YAAW;kDAC9C,cAAA,8VAAC;4CAAI,WAAU;;8DACb,8VAAC;oDACC,OAAM;oDACN,SAAS,QAAQ,OAAO,IAAI;oDAC5B,UAAU,IAAM,aAAa,WAAW,CAAC,QAAQ,OAAO;;;;;;8DAE1D,8VAAC;oDACC,OAAM;oDACN,SAAS,QAAQ,QAAQ,IAAI;oDAC7B,UAAU,IAAM,aAAa,YAAY,CAAC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhF;GA7QgB;KAAA", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/app/products/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useSearchParams } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { \n  MagnifyingGlassIcon,\n  AdjustmentsHorizontalIcon,\n  Squares2X2Icon,\n  ListBulletIcon\n} from '@heroicons/react/24/outline';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { ProductGrid } from '@/components/products/product-grid';\nimport { ProductFilters } from '@/components/products/product-filters';\nimport { Product, ProductFilters as FilterType } from '@/types';\n\n// Mock data for demonstration\nconst mockProducts: Product[] = [\n  {\n    id: '1',\n    name: 'Elegant Portrait - Classic Style',\n    description: 'A beautiful bedazzled portrait with classic rhinestone arrangement',\n    category: 'pre-made',\n    images: ['/products/portrait1.jpg'],\n    variants: [\n      {\n        id: 'v1',\n        size: 'medium',\n        bedazzlingLevel: 'medium',\n        frameOption: 'premium',\n        price: 299.99,\n        stock: 5\n      }\n    ],\n    basePrice: 299.99,\n    featured: true,\n    tags: ['elegant', 'classic', 'portrait'],\n    rating: 4.8,\n    reviewCount: 24,\n    createdAt: new Date(),\n    updatedAt: new Date()\n  },\n  {\n    id: '2',\n    name: 'Glamorous Bedazzled Art',\n    description: 'Stunning rhinestone artwork with premium crystals',\n    category: 'pre-made',\n    images: ['/products/portrait2.jpg'],\n    variants: [\n      {\n        id: 'v2',\n        size: 'large',\n        bedazzlingLevel: 'heavy',\n        frameOption: 'luxury',\n        price: 499.99,\n        stock: 3\n      }\n    ],\n    basePrice: 499.99,\n    featured: false,\n    tags: ['glamorous', 'premium', 'art'],\n    rating: 4.9,\n    reviewCount: 18,\n    createdAt: new Date(),\n    updatedAt: new Date()\n  },\n  // Add more mock products as needed\n];\n\nexport default function ProductsPage() {\n  const searchParams = useSearchParams();\n  const [products, setProducts] = useState<Product[]>(mockProducts);\n  const [filteredProducts, setFilteredProducts] = useState<Product[]>(mockProducts);\n  const [isLoading, setIsLoading] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');\n  const [filtersOpen, setFiltersOpen] = useState(false);\n  const [sortBy, setSortBy] = useState('newest');\n  \n  const [filters, setFilters] = useState<FilterType>({\n    category: searchParams.get('category') as any || undefined,\n    size: [],\n    bedazzlingLevel: [],\n    frameOption: [],\n    priceRange: undefined,\n    rating: undefined,\n    inStock: undefined,\n    featured: undefined,\n  });\n\n  // Apply filters and search\n  useEffect(() => {\n    let filtered = [...products];\n\n    // Search filter\n    if (searchQuery) {\n      filtered = filtered.filter(product =>\n        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        product.description.toLowerCase().includes(searchQuery.toLowerCase()) ||\n        product.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))\n      );\n    }\n\n    // Category filter\n    if (filters.category) {\n      filtered = filtered.filter(product => product.category === filters.category);\n    }\n\n    // Size filter\n    if (filters.size && filters.size.length > 0) {\n      filtered = filtered.filter(product =>\n        product.variants.some(variant => filters.size!.includes(variant.size))\n      );\n    }\n\n    // Bedazzling level filter\n    if (filters.bedazzlingLevel && filters.bedazzlingLevel.length > 0) {\n      filtered = filtered.filter(product =>\n        product.variants.some(variant => filters.bedazzlingLevel!.includes(variant.bedazzlingLevel))\n      );\n    }\n\n    // Frame option filter\n    if (filters.frameOption && filters.frameOption.length > 0) {\n      filtered = filtered.filter(product =>\n        product.variants.some(variant => filters.frameOption!.includes(variant.frameOption))\n      );\n    }\n\n    // Price range filter\n    if (filters.priceRange) {\n      filtered = filtered.filter(product =>\n        product.basePrice >= filters.priceRange![0] && \n        product.basePrice <= filters.priceRange![1]\n      );\n    }\n\n    // Rating filter\n    if (filters.rating) {\n      filtered = filtered.filter(product => product.rating >= filters.rating!);\n    }\n\n    // In stock filter\n    if (filters.inStock) {\n      filtered = filtered.filter(product =>\n        product.variants.some(variant => variant.stock > 0)\n      );\n    }\n\n    // Featured filter\n    if (filters.featured) {\n      filtered = filtered.filter(product => product.featured);\n    }\n\n    // Sort products\n    switch (sortBy) {\n      case 'price-low':\n        filtered.sort((a, b) => a.basePrice - b.basePrice);\n        break;\n      case 'price-high':\n        filtered.sort((a, b) => b.basePrice - a.basePrice);\n        break;\n      case 'rating':\n        filtered.sort((a, b) => b.rating - a.rating);\n        break;\n      case 'popular':\n        filtered.sort((a, b) => b.reviewCount - a.reviewCount);\n        break;\n      case 'newest':\n      default:\n        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n        break;\n    }\n\n    setFilteredProducts(filtered);\n  }, [products, searchQuery, filters, sortBy]);\n\n  const handleFiltersChange = (newFilters: FilterType) => {\n    setFilters(newFilters);\n  };\n\n  const handleClearFilters = () => {\n    setFilters({\n      category: undefined,\n      size: [],\n      bedazzlingLevel: [],\n      frameOption: [],\n      priceRange: undefined,\n      rating: undefined,\n      inStock: undefined,\n      featured: undefined,\n    });\n    setSearchQuery('');\n  };\n\n  const handleAddToWishlist = (productId: string) => {\n    // Implement wishlist functionality\n    console.log('Add to wishlist:', productId);\n  };\n\n  const handleAddToCart = (productId: string, variantId: string) => {\n    // Implement cart functionality\n    console.log('Add to cart:', productId, variantId);\n  };\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Page Header */}\n      <div className=\"mb-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          <h1 className=\"text-3xl lg:text-4xl font-bold text-foreground mb-4\">\n            Bedazzled Portraits\n          </h1>\n          <p className=\"text-lg text-muted-foreground max-w-2xl\">\n            Discover our stunning collection of rhinestone bedazzled portraits. \n            Each piece is carefully crafted with premium materials and attention to detail.\n          </p>\n        </motion.div>\n      </div>\n\n      {/* Search and Controls */}\n      <div className=\"mb-6 space-y-4\">\n        <div className=\"flex flex-col lg:flex-row gap-4\">\n          {/* Search */}\n          <div className=\"flex-1 relative\">\n            <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n            <Input\n              type=\"search\"\n              placeholder=\"Search for bedazzled portraits...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n\n          {/* Sort */}\n          <div className=\"flex items-center gap-2\">\n            <select\n              value={sortBy}\n              onChange={(e) => setSortBy(e.target.value)}\n              className=\"px-3 py-2 border border-border rounded-md bg-background text-foreground\"\n            >\n              <option value=\"newest\">Newest</option>\n              <option value=\"price-low\">Price: Low to High</option>\n              <option value=\"price-high\">Price: High to Low</option>\n              <option value=\"rating\">Highest Rated</option>\n              <option value=\"popular\">Most Popular</option>\n            </select>\n\n            {/* View Mode Toggle */}\n            <div className=\"hidden sm:flex border border-border rounded-md\">\n              <Button\n                variant={viewMode === 'grid' ? 'default' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setViewMode('grid')}\n                className=\"rounded-r-none\"\n              >\n                <Squares2X2Icon className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant={viewMode === 'list' ? 'default' : 'ghost'}\n                size=\"sm\"\n                onClick={() => setViewMode('list')}\n                className=\"rounded-l-none\"\n              >\n                <ListBulletIcon className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Results Count */}\n        <div className=\"flex items-center justify-between\">\n          <p className=\"text-sm text-muted-foreground\">\n            Showing {filteredProducts.length} of {products.length} products\n          </p>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-8\">\n        {/* Filters Sidebar */}\n        <div className=\"lg:col-span-1\">\n          <ProductFilters\n            filters={filters}\n            onFiltersChange={handleFiltersChange}\n            onClearFilters={handleClearFilters}\n            isOpen={filtersOpen}\n            onToggle={() => setFiltersOpen(!filtersOpen)}\n          />\n        </div>\n\n        {/* Products Grid */}\n        <div className=\"lg:col-span-3\">\n          <ProductGrid\n            products={filteredProducts}\n            isLoading={isLoading}\n            onAddToWishlist={handleAddToWishlist}\n            onAddToCart={handleAddToCart}\n          />\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAMA;AACA;AACA;AACA;;;AAdA;;;;;;;;;AAiBA,8BAA8B;AAC9B,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,QAAQ;YAAC;SAA0B;QACnC,UAAU;YACR;gBACE,IAAI;gBACJ,MAAM;gBACN,iBAAiB;gBACjB,aAAa;gBACb,OAAO;gBACP,OAAO;YACT;SACD;QACD,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAW;YAAW;SAAW;QACxC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,UAAU;QACV,QAAQ;YAAC;SAA0B;QACnC,UAAU;YACR;gBACE,IAAI;gBACJ,MAAM;gBACN,iBAAiB;gBACjB,aAAa;gBACb,OAAO;gBACP,OAAO;YACT;SACD;QACD,WAAW;QACX,UAAU;QACV,MAAM;YAAC;YAAa;YAAW;SAAM;QACrC,QAAQ;QACR,aAAa;QACb,WAAW,IAAI;QACf,WAAW,IAAI;IACjB;CAED;AAEc,SAAS;;IACtB,MAAM,eAAe,CAAA,GAAA,sSAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,8TAAA,CAAA,WAAQ,AAAD,EAAa;IACpD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,8TAAA,CAAA,WAAQ,AAAD,EAAa;IACpE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8TAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8TAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,8TAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,8TAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,8TAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,8TAAA,CAAA,WAAQ,AAAD,EAAc;QACjD,UAAU,aAAa,GAAG,CAAC,eAAsB;QACjD,MAAM,EAAE;QACR,iBAAiB,EAAE;QACnB,aAAa,EAAE;QACf,YAAY;QACZ,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IAEA,2BAA2B;IAC3B,CAAA,GAAA,8TAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,WAAW;mBAAI;aAAS;YAE5B,gBAAgB;YAChB,IAAI,aAAa;gBACf,WAAW,SAAS,MAAM;8CAAC,CAAA,UACzB,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAClE,QAAQ,IAAI,CAAC,IAAI;sDAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;;;YAE/E;YAEA,kBAAkB;YAClB,IAAI,QAAQ,QAAQ,EAAE;gBACpB,WAAW,SAAS,MAAM;8CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK,QAAQ,QAAQ;;YAC7E;YAEA,cAAc;YACd,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,GAAG;gBAC3C,WAAW,SAAS,MAAM;8CAAC,CAAA,UACzB,QAAQ,QAAQ,CAAC,IAAI;sDAAC,CAAA,UAAW,QAAQ,IAAI,CAAE,QAAQ,CAAC,QAAQ,IAAI;;;YAExE;YAEA,0BAA0B;YAC1B,IAAI,QAAQ,eAAe,IAAI,QAAQ,eAAe,CAAC,MAAM,GAAG,GAAG;gBACjE,WAAW,SAAS,MAAM;8CAAC,CAAA,UACzB,QAAQ,QAAQ,CAAC,IAAI;sDAAC,CAAA,UAAW,QAAQ,eAAe,CAAE,QAAQ,CAAC,QAAQ,eAAe;;;YAE9F;YAEA,sBAAsB;YACtB,IAAI,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,GAAG;gBACzD,WAAW,SAAS,MAAM;8CAAC,CAAA,UACzB,QAAQ,QAAQ,CAAC,IAAI;sDAAC,CAAA,UAAW,QAAQ,WAAW,CAAE,QAAQ,CAAC,QAAQ,WAAW;;;YAEtF;YAEA,qBAAqB;YACrB,IAAI,QAAQ,UAAU,EAAE;gBACtB,WAAW,SAAS,MAAM;8CAAC,CAAA,UACzB,QAAQ,SAAS,IAAI,QAAQ,UAAU,AAAC,CAAC,EAAE,IAC3C,QAAQ,SAAS,IAAI,QAAQ,UAAU,AAAC,CAAC,EAAE;;YAE/C;YAEA,gBAAgB;YAChB,IAAI,QAAQ,MAAM,EAAE;gBAClB,WAAW,SAAS,MAAM;8CAAC,CAAA,UAAW,QAAQ,MAAM,IAAI,QAAQ,MAAM;;YACxE;YAEA,kBAAkB;YAClB,IAAI,QAAQ,OAAO,EAAE;gBACnB,WAAW,SAAS,MAAM;8CAAC,CAAA,UACzB,QAAQ,QAAQ,CAAC,IAAI;sDAAC,CAAA,UAAW,QAAQ,KAAK,GAAG;;;YAErD;YAEA,kBAAkB;YAClB,IAAI,QAAQ,QAAQ,EAAE;gBACpB,WAAW,SAAS,MAAM;8CAAC,CAAA,UAAW,QAAQ,QAAQ;;YACxD;YAEA,gBAAgB;YAChB,OAAQ;gBACN,KAAK;oBACH,SAAS,IAAI;kDAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;;oBACjD;gBACF,KAAK;oBACH,SAAS,IAAI;kDAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;;oBACjD;gBACF,KAAK;oBACH,SAAS,IAAI;kDAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;;oBAC3C;gBACF,KAAK;oBACH,SAAS,IAAI;kDAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;;oBACrD;gBACF,KAAK;gBACL;oBACE,SAAS,IAAI;kDAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;;oBACvF;YACJ;YAEA,oBAAoB;QACtB;iCAAG;QAAC;QAAU;QAAa;QAAS;KAAO;IAE3C,MAAM,sBAAsB,CAAC;QAC3B,WAAW;IACb;IAEA,MAAM,qBAAqB;QACzB,WAAW;YACT,UAAU;YACV,MAAM,EAAE;YACR,iBAAiB,EAAE;YACnB,aAAa,EAAE;YACf,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,UAAU;QACZ;QACA,eAAe;IACjB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mCAAmC;QACnC,QAAQ,GAAG,CAAC,oBAAoB;IAClC;IAEA,MAAM,kBAAkB,CAAC,WAAmB;QAC1C,+BAA+B;QAC/B,QAAQ,GAAG,CAAC,gBAAgB,WAAW;IACzC;IAEA,qBACE,8VAAC;QAAI,WAAU;;0BAEb,8VAAC;gBAAI,WAAU;0BACb,cAAA,8VAAC,yUAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8VAAC;4BAAG,WAAU;sCAAsD;;;;;;sCAGpE,8VAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAQ3D,8VAAC;gBAAI,WAAU;;kCACb,8VAAC;wBAAI,WAAU;;0CAEb,8VAAC;gCAAI,WAAU;;kDACb,8VAAC,+TAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;kDAC/B,8VAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAKd,8VAAC;gCAAI,WAAU;;kDACb,8VAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;;0DAEV,8VAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8VAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8VAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,8VAAC;gDAAO,OAAM;0DAAS;;;;;;0DACvB,8VAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;kDAI1B,8VAAC;wCAAI,WAAU;;0DACb,8VAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,8VAAC,qTAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;0DAE5B,8VAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,aAAa,SAAS,YAAY;gDAC3C,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DAEV,cAAA,8VAAC,qTAAA,CAAA,iBAAc;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,8VAAC;wBAAI,WAAU;kCACb,cAAA,8VAAC;4BAAE,WAAU;;gCAAgC;gCAClC,iBAAiB,MAAM;gCAAC;gCAAK,SAAS,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAM5D,8VAAC;gBAAI,WAAU;;kCAEb,8VAAC;wBAAI,WAAU;kCACb,cAAA,8VAAC,uJAAA,CAAA,iBAAc;4BACb,SAAS;4BACT,iBAAiB;4BACjB,gBAAgB;4BAChB,QAAQ;4BACR,UAAU,IAAM,eAAe,CAAC;;;;;;;;;;;kCAKpC,8VAAC;wBAAI,WAAU;kCACb,cAAA,8VAAC,oJAAA,CAAA,cAAW;4BACV,UAAU;4BACV,WAAW;4BACX,iBAAiB;4BACjB,aAAa;;;;;;;;;;;;;;;;;;;;;;;AAMzB;GA/OwB;;QACD,sSAAA,CAAA,kBAAe;;;KADd", "debugId": null}}]}