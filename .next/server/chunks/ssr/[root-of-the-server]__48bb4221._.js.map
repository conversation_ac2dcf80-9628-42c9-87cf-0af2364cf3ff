{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\n/**\n * Utility function to merge Tailwind CSS classes\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Format price to currency string\n */\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(price);\n}\n\n/**\n * Generate a random ID\n */\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * Capitalize first letter of a string\n */\nexport function capitalize(str: string): string {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\n/**\n * Truncate text to specified length\n */\nexport function truncate(text: string, length: number): string {\n  if (text.length <= length) return text;\n  return text.slice(0, length) + '...';\n}\n\n/**\n * Check if email is valid\n */\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Format date to readable string\n */\nexport function formatDate(date: Date | string): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\n/**\n * Calculate discount percentage\n */\nexport function calculateDiscount(originalPrice: number, salePrice: number): number {\n  return Math.round(((originalPrice - salePrice) / originalPrice) * 100);\n}\n\n/**\n * Generate slug from string\n */\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAKO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAKO,SAAS,WAAW,GAAW;IACpC,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AACjD;AAKO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAKO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAKO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,kBAAkB,aAAqB,EAAE,SAAiB;IACxE,OAAO,KAAK,KAAK,CAAC,AAAC,CAAC,gBAAgB,SAAS,IAAI,gBAAiB;AACpE;AAKO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/components/ui/button.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-white hover:bg-primary-light',\n        destructive: 'bg-error text-white hover:bg-error/90',\n        outline: 'border border-border bg-background hover:bg-muted hover:text-foreground',\n        secondary: 'bg-accent text-white hover:bg-accent-light',\n        ghost: 'hover:bg-muted hover:text-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-md px-3',\n        lg: 'h-11 rounded-md px-8',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,qQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,sWAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,+YAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/components/ui/input.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAQA,MAAM,sBAAQ,sWAAA,CAAA,aAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,+YAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/components/ui/badge.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default: 'border-transparent bg-primary text-white hover:bg-primary-light',\n        secondary: 'border-transparent bg-accent text-white hover:bg-accent-light',\n        destructive: 'border-transparent bg-error text-white hover:bg-error/80',\n        outline: 'text-foreground border-border',\n        success: 'border-transparent bg-success text-white hover:bg-success/80',\n        warning: 'border-transparent bg-warning text-white hover:bg-warning/80',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,4KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,+YAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  ShoppingCartIcon, \n  HeartIcon, \n  UserIcon, \n  MagnifyingGlassIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { cn } from '@/lib/utils';\n\ninterface HeaderProps {\n  cartItemCount?: number;\n  wishlistItemCount?: number;\n  user?: {\n    name: string;\n    avatar?: string;\n  } | null;\n}\n\nexport function Header({ cartItemCount = 0, wishlistItemCount = 0, user }: HeaderProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\n\n  const navigation = [\n    { name: 'Home', href: '/' },\n    { name: 'Pre-made', href: '/products?category=pre-made' },\n    { name: 'Custom Orders', href: '/custom-orders' },\n    { name: 'Gallery', href: '/gallery' },\n    { name: 'About', href: '/about' },\n  ];\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-primary to-accent\">\n              <span className=\"text-sm font-bold text-white\">B</span>\n            </div>\n            <span className=\"text-xl font-bold text-primary\">Bedazzled</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-sm font-medium text-foreground hover:text-accent transition-colors\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* Search Bar - Desktop */}\n          <div className=\"hidden lg:flex flex-1 max-w-md mx-8\">\n            <div className=\"relative w-full\">\n              <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n              <Input\n                type=\"search\"\n                placeholder=\"Search for bedazzled portraits...\"\n                className=\"pl-10 pr-4\"\n              />\n            </div>\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Search Button - Mobile */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"lg:hidden\"\n              onClick={() => setIsSearchOpen(!isSearchOpen)}\n            >\n              <MagnifyingGlassIcon className=\"h-5 w-5\" />\n            </Button>\n\n            {/* Wishlist */}\n            <Link href=\"/wishlist\">\n              <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n                <HeartIcon className=\"h-5 w-5\" />\n                {wishlistItemCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs\"\n                  >\n                    {wishlistItemCount}\n                  </Badge>\n                )}\n              </Button>\n            </Link>\n\n            {/* Cart */}\n            <Link href=\"/cart\">\n              <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n                <ShoppingCartIcon className=\"h-5 w-5\" />\n                {cartItemCount > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -right-1 -top-1 h-5 w-5 rounded-full p-0 text-xs\"\n                  >\n                    {cartItemCount}\n                  </Badge>\n                )}\n              </Button>\n            </Link>\n\n            {/* User Menu */}\n            {user ? (\n              <Button variant=\"ghost\" size=\"icon\">\n                {user.avatar ? (\n                  <img\n                    src={user.avatar}\n                    alt={user.name}\n                    className=\"h-6 w-6 rounded-full\"\n                  />\n                ) : (\n                  <UserIcon className=\"h-5 w-5\" />\n                )}\n              </Button>\n            ) : (\n              <Link href=\"/auth/login\">\n                <Button variant=\"outline\" size=\"sm\">\n                  Sign In\n                </Button>\n              </Link>\n            )}\n\n            {/* Mobile Menu Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"md:hidden\"\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            >\n              {isMobileMenuOpen ? (\n                <XMarkIcon className=\"h-5 w-5\" />\n              ) : (\n                <Bars3Icon className=\"h-5 w-5\" />\n              )}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Search */}\n        <AnimatePresence>\n          {isSearchOpen && (\n            <motion.div\n              initial={{ height: 0, opacity: 0 }}\n              animate={{ height: 'auto', opacity: 1 }}\n              exit={{ height: 0, opacity: 0 }}\n              className=\"lg:hidden border-t border-border\"\n            >\n              <div className=\"py-4\">\n                <div className=\"relative\">\n                  <MagnifyingGlassIcon className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n                  <Input\n                    type=\"search\"\n                    placeholder=\"Search for bedazzled portraits...\"\n                    className=\"pl-10 pr-4\"\n                  />\n                </div>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isMobileMenuOpen && (\n            <motion.div\n              initial={{ height: 0, opacity: 0 }}\n              animate={{ height: 'auto', opacity: 1 }}\n              exit={{ height: 0, opacity: 0 }}\n              className=\"md:hidden border-t border-border\"\n            >\n              <nav className=\"py-4 space-y-2\">\n                {navigation.map((item) => (\n                  <Link\n                    key={item.name}\n                    href={item.href}\n                    className=\"block px-4 py-2 text-sm font-medium text-foreground hover:bg-muted rounded-md transition-colors\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                ))}\n              </nav>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AAfA;;;;;;;;;AA2BO,SAAS,OAAO,EAAE,gBAAgB,CAAC,EAAE,oBAAoB,CAAC,EAAE,IAAI,EAAe;IACpF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,sWAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,aAAa;QACjB;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAY,MAAM;QAA8B;QACxD;YAAE,MAAM;YAAiB,MAAM;QAAiB;QAChD;YAAE,MAAM;YAAW,MAAM;QAAW;QACpC;YAAE,MAAM;YAAS,MAAM;QAAS;KACjC;IAED,qBACE,+YAAC;QAAO,WAAU;kBAChB,cAAA,+YAAC;YAAI,WAAU;;8BACb,+YAAC;oBAAI,WAAU;;sCAEb,+YAAC,6TAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,+YAAC;oCAAI,WAAU;8CACb,cAAA,+YAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,+YAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;;sCAInD,+YAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,+YAAC,6TAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,+YAAC;4BAAI,WAAU;sCACb,cAAA,+YAAC;gCAAI,WAAU;;kDACb,+YAAC,4TAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;kDAC/B,+YAAC,iIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,+YAAC;4BAAI,WAAU;;8CAEb,+YAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,gBAAgB,CAAC;8CAEhC,cAAA,+YAAC,4TAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;;;;;;8CAIjC,+YAAC,6TAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,+YAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,WAAU;;0DAC5C,+YAAC,wSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CACpB,oBAAoB,mBACnB,+YAAC,iIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET;;;;;;;;;;;;;;;;;8CAOT,+YAAC,6TAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,+YAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,WAAU;;0DAC5C,+YAAC,sTAAA,CAAA,mBAAgB;gDAAC,WAAU;;;;;;4CAC3B,gBAAgB,mBACf,+YAAC,iIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET;;;;;;;;;;;;;;;;;gCAOR,qBACC,+YAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAC1B,KAAK,MAAM,iBACV,+YAAC;wCACC,KAAK,KAAK,MAAM;wCAChB,KAAK,KAAK,IAAI;wCACd,WAAU;;;;;6DAGZ,+YAAC,sSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;yDAIxB,+YAAC,6TAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,+YAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;8CAOxC,+YAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,oBAAoB,CAAC;8CAEnC,iCACC,+YAAC,wSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,+YAAC,wSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAO7B,+YAAC,qUAAA,CAAA,kBAAe;8BACb,8BACC,+YAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBACjC,SAAS;4BAAE,QAAQ;4BAAQ,SAAS;wBAAE;wBACtC,MAAM;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBAC9B,WAAU;kCAEV,cAAA,+YAAC;4BAAI,WAAU;sCACb,cAAA,+YAAC;gCAAI,WAAU;;kDACb,+YAAC,4TAAA,CAAA,sBAAmB;wCAAC,WAAU;;;;;;kDAC/B,+YAAC,iIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAStB,+YAAC,qUAAA,CAAA,kBAAe;8BACb,kCACC,+YAAC,sUAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBACjC,SAAS;4BAAE,QAAQ;4BAAQ,SAAS;wBAAE;wBACtC,MAAM;4BAAE,QAAQ;4BAAG,SAAS;wBAAE;wBAC9B,WAAU;kCAEV,cAAA,+YAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,+YAAC,6TAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,oBAAoB;8CAElC,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAelC", "debugId": null}}, {"offset": {"line": 644, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/clones/dazzled/src/components/layout/footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { \n  EnvelopeIcon, \n  PhoneIcon, \n  MapPinIcon \n} from '@heroicons/react/24/outline';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\n\nexport function Footer() {\n  const footerSections = [\n    {\n      title: 'Shop',\n      links: [\n        { name: 'Pre-made Portraits', href: '/products?category=pre-made' },\n        { name: 'Custom Orders', href: '/custom-orders' },\n        { name: 'Gallery', href: '/gallery' },\n        { name: 'Size Guide', href: '/size-guide' },\n      ],\n    },\n    {\n      title: 'Customer Care',\n      links: [\n        { name: 'Contact Us', href: '/contact' },\n        { name: 'FAQ', href: '/faq' },\n        { name: 'Shipping Info', href: '/shipping' },\n        { name: 'Returns', href: '/returns' },\n      ],\n    },\n    {\n      title: 'Company',\n      links: [\n        { name: 'About Us', href: '/about' },\n        { name: 'Our Process', href: '/process' },\n        { name: 'Reviews', href: '/reviews' },\n        { name: 'Blog', href: '/blog' },\n      ],\n    },\n    {\n      title: 'Legal',\n      links: [\n        { name: 'Privacy Policy', href: '/privacy' },\n        { name: 'Terms of Service', href: '/terms' },\n        { name: 'Cookie Policy', href: '/cookies' },\n        { name: 'Refund Policy', href: '/refunds' },\n      ],\n    },\n  ];\n\n  return (\n    <footer className=\"bg-muted border-t border-border\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-primary to-accent\">\n                <span className=\"text-sm font-bold text-white\">B</span>\n              </div>\n              <span className=\"text-xl font-bold text-primary\">Bedazzled</span>\n            </Link>\n            <p className=\"text-sm text-muted-foreground mb-6 max-w-md\">\n              Transform your memories into stunning rhinestone bedazzled portraits. \n              Each piece is carefully crafted with premium materials and attention to detail.\n            </p>\n            \n            {/* Contact Info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                <EnvelopeIcon className=\"h-4 w-4\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                <PhoneIcon className=\"h-4 w-4\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                <MapPinIcon className=\"h-4 w-4\" />\n                <span>New York, NY</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h3 className=\"font-semibold text-foreground mb-4\">{section.title}</h3>\n              <ul className=\"space-y-2\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        {/* Newsletter Section */}\n        <div className=\"border-t border-border mt-12 pt-8\">\n          <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-6\">\n            <div>\n              <h3 className=\"font-semibold text-foreground mb-2\">Stay Updated</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                Get the latest updates on new products and exclusive offers.\n              </p>\n            </div>\n            <div className=\"flex flex-col sm:flex-row gap-2 max-w-md\">\n              <Input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1\"\n              />\n              <Button type=\"submit\" className=\"sm:w-auto\">\n                Subscribe\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-border mt-8 pt-8 flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\n          <p className=\"text-sm text-muted-foreground\">\n            © 2024 Bedazzled. All rights reserved.\n          </p>\n          \n          {/* Social Links */}\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              href=\"#\"\n              className=\"text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              <span className=\"sr-only\">Instagram</span>\n              <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987s11.987-5.367 11.987-11.987C24.004 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.875-.875 2.026-1.297 3.323-1.297s2.448.422 3.323 1.297c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.781c-.49 0-.928-.422-.928-.928 0-.49.438-.928.928-.928.49 0 .928.438.928.928 0 .506-.438.928-.928.928zm-7.83 1.297c1.297 0 2.346 1.049 2.346 2.346s-1.049 2.346-2.346 2.346-2.346-1.049-2.346-2.346 1.049-2.346 2.346-2.346z\"/>\n              </svg>\n            </Link>\n            <Link\n              href=\"#\"\n              className=\"text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              <span className=\"sr-only\">Facebook</span>\n              <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n              </svg>\n            </Link>\n            <Link\n              href=\"#\"\n              className=\"text-muted-foreground hover:text-foreground transition-colors\"\n            >\n              <span className=\"sr-only\">Pinterest</span>\n              <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.097.118.112.221.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624.001 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\"/>\n              </svg>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAKA;AACA;AAVA;;;;;;AAYO,SAAS;IACd,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAsB,MAAM;gBAA8B;gBAClE;oBAAE,MAAM;oBAAiB,MAAM;gBAAiB;gBAChD;oBAAE,MAAM;oBAAW,MAAM;gBAAW;gBACpC;oBAAE,MAAM;oBAAc,MAAM;gBAAc;aAC3C;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAc,MAAM;gBAAW;gBACvC;oBAAE,MAAM;oBAAO,MAAM;gBAAO;gBAC5B;oBAAE,MAAM;oBAAiB,MAAM;gBAAY;gBAC3C;oBAAE,MAAM;oBAAW,MAAM;gBAAW;aACrC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAS;gBACnC;oBAAE,MAAM;oBAAe,MAAM;gBAAW;gBACxC;oBAAE,MAAM;oBAAW,MAAM;gBAAW;gBACpC;oBAAE,MAAM;oBAAQ,MAAM;gBAAQ;aAC/B;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAkB,MAAM;gBAAW;gBAC3C;oBAAE,MAAM;oBAAoB,MAAM;gBAAS;gBAC3C;oBAAE,MAAM;oBAAiB,MAAM;gBAAW;gBAC1C;oBAAE,MAAM;oBAAiB,MAAM;gBAAW;aAC3C;QACH;KACD;IAED,qBACE,+YAAC;QAAO,WAAU;kBAChB,cAAA,+YAAC;YAAI,WAAU;;8BACb,+YAAC;oBAAI,WAAU;;sCAEb,+YAAC;4BAAI,WAAU;;8CACb,+YAAC,6TAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,+YAAC;4CAAI,WAAU;sDACb,cAAA,+YAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,+YAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;8CAEnD,+YAAC;oCAAE,WAAU;8CAA8C;;;;;;8CAM3D,+YAAC;oCAAI,WAAU;;sDACb,+YAAC;4CAAI,WAAU;;8DACb,+YAAC,8SAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;8DACxB,+YAAC;8DAAK;;;;;;;;;;;;sDAER,+YAAC;4CAAI,WAAU;;8DACb,+YAAC,wSAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,+YAAC;8DAAK;;;;;;;;;;;;sDAER,+YAAC;4CAAI,WAAU;;8DACb,+YAAC,0SAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,+YAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;wBAMX,eAAe,GAAG,CAAC,CAAC,wBACnB,+YAAC;;kDACC,+YAAC;wCAAG,WAAU;kDAAsC,QAAQ,KAAK;;;;;;kDACjE,+YAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,+YAAC;0DACC,cAAA,+YAAC,6TAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAmB3B,+YAAC;oBAAI,WAAU;8BACb,cAAA,+YAAC;wBAAI,WAAU;;0CACb,+YAAC;;kDACC,+YAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,+YAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAI/C,+YAAC;gCAAI,WAAU;;kDACb,+YAAC,iIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,+YAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,WAAU;kDAAY;;;;;;;;;;;;;;;;;;;;;;;8BAQlD,+YAAC;oBAAI,WAAU;;sCACb,+YAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAK7C,+YAAC;4BAAI,WAAU;;8CACb,+YAAC,6TAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,+YAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,+YAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,+YAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;8CAGZ,+YAAC,6TAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,+YAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,+YAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,+YAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;8CAGZ,+YAAC,6TAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,+YAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,+YAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,+YAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxB", "debugId": null}}]}