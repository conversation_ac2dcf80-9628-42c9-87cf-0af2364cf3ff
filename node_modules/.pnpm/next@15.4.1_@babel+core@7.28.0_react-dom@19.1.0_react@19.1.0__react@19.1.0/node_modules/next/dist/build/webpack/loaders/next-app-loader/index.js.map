{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-app-loader/index.ts"], "sourcesContent": ["import type webpack from 'next/dist/compiled/webpack/webpack'\nimport {\n  UNDERSCORE_NOT_FOUND_ROUTE,\n  UNDERSCORE_NOT_FOUND_ROUTE_ENTRY,\n  type ValueOf,\n} from '../../../../shared/lib/constants'\nimport type { ModuleTuple, CollectedMetadata } from '../metadata/types'\n\nimport path from 'path'\nimport { bold } from '../../../../lib/picocolors'\nimport { getModuleBuildInfo } from '../get-module-build-info'\nimport { verifyRootLayout } from '../../../../lib/verify-root-layout'\nimport * as Log from '../../../output/log'\nimport { APP_DIR_ALIAS } from '../../../../lib/constants'\nimport {\n  createMetadataExportsCode,\n  createStaticMetadataFromRoute,\n} from '../metadata/discover'\nimport { promises as fs } from 'fs'\nimport { isAppRouteRoute } from '../../../../lib/is-app-route-route'\nimport type { NextConfig } from '../../../../server/config-shared'\nimport { AppPathnameNormalizer } from '../../../../server/normalizers/built/app/app-pathname-normalizer'\nimport type { MiddlewareConfig } from '../../../analysis/get-page-static-info'\nimport { isAppBuiltinNotFoundPage } from '../../../utils'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport {\n  isGroupSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../../../../shared/lib/segment'\nimport { getFilesInDir } from '../../../../lib/get-files-in-dir'\nimport type { PageExtensions } from '../../../page-extensions-type'\nimport { PARALLEL_ROUTE_DEFAULT_PATH } from '../../../../client/components/builtin/default'\nimport type { Compilation } from 'webpack'\nimport { createAppRouteCode } from './create-app-route-code'\n\nexport type AppLoaderOptions = {\n  name: string\n  page: string\n  pagePath: string\n  appDir: string\n  appPaths: readonly string[] | null\n  preferredRegion: string | string[] | undefined\n  pageExtensions: PageExtensions\n  assetPrefix: string\n  rootDir?: string\n  tsconfigPath?: string\n  isDev?: true\n  basePath: string\n  nextConfigOutput?: NextConfig['output']\n  middlewareConfig: string\n  isGlobalNotFoundEnabled: true | undefined\n}\ntype AppLoader = webpack.LoaderDefinitionFunction<AppLoaderOptions>\n\nconst HTTP_ACCESS_FALLBACKS = {\n  'not-found': 'not-found',\n  forbidden: 'forbidden',\n  unauthorized: 'unauthorized',\n} as const\nconst defaultHTTPAccessFallbackPaths = {\n  'not-found': 'next/dist/client/components/builtin/not-found.js',\n  forbidden: 'next/dist/client/components/builtin/forbidden.js',\n  unauthorized: 'next/dist/client/components/builtin/unauthorized.js',\n} as const\n\nconst FILE_TYPES = {\n  layout: 'layout',\n  template: 'template',\n  error: 'error',\n  loading: 'loading',\n  'global-error': 'global-error',\n  'global-not-found': 'global-not-found',\n  ...HTTP_ACCESS_FALLBACKS,\n} as const\n\nconst GLOBAL_ERROR_FILE_TYPE = 'global-error'\nconst GLOBAL_NOT_FOUND_FILE_TYPE = 'global-not-found'\nconst PAGE_SEGMENT = 'page$'\nconst PARALLEL_CHILDREN_SEGMENT = 'children$'\n\nconst defaultGlobalErrorPath =\n  'next/dist/client/components/builtin/global-error.js'\nconst defaultNotFoundPath = 'next/dist/client/components/builtin/not-found.js'\nconst defaultLayoutPath = 'next/dist/client/components/builtin/layout.js'\nconst defaultGlobalNotFoundPath =\n  'next/dist/client/components/builtin/global-not-found.js'\n\ntype DirResolver = (pathToResolve: string) => string\ntype PathResolver = (\n  pathname: string\n) => Promise<string | undefined> | string | undefined\nexport type MetadataResolver = (\n  dir: string,\n  filename: string,\n  extensions: readonly string[]\n) => Promise<string | undefined>\n\nexport type AppDirModules = {\n  readonly [moduleKey in ValueOf<typeof FILE_TYPES>]?: ModuleTuple\n} & {\n  readonly page?: ModuleTuple\n} & {\n  readonly metadata?: CollectedMetadata\n} & {\n  readonly defaultPage?: ModuleTuple\n}\n\nconst normalizeParallelKey = (key: string) =>\n  key.startsWith('@') ? key.slice(1) : key\n\nconst isDirectory = async (pathname: string) => {\n  try {\n    const stat = await fs.stat(pathname)\n    return stat.isDirectory()\n  } catch (err) {\n    return false\n  }\n}\n\nasync function createTreeCodeFromPath(\n  pagePath: string,\n  {\n    page,\n    resolveDir,\n    resolver,\n    resolveParallelSegments,\n    metadataResolver,\n    pageExtensions,\n    basePath,\n    collectedDeclarations,\n    isGlobalNotFoundEnabled,\n  }: {\n    page: string\n    resolveDir: DirResolver\n    resolver: PathResolver\n    metadataResolver: MetadataResolver\n    resolveParallelSegments: (\n      pathname: string\n    ) => [key: string, segment: string | string[]][]\n    loaderContext: webpack.LoaderContext<AppLoaderOptions>\n    pageExtensions: PageExtensions\n    basePath: string\n    collectedDeclarations: [string, string][]\n    isGlobalNotFoundEnabled: boolean\n  }\n): Promise<{\n  treeCode: string\n  pages: string\n  rootLayout: string | undefined\n  globalError: string\n  globalNotFound: string\n}> {\n  const splittedPath = pagePath.split(/[\\\\/]/, 1)\n  const isNotFoundRoute = page === UNDERSCORE_NOT_FOUND_ROUTE_ENTRY\n  const isDefaultNotFound = isAppBuiltinNotFoundPage(pagePath)\n\n  const appDirPrefix = isDefaultNotFound ? APP_DIR_ALIAS : splittedPath[0]\n  const pages: string[] = []\n\n  let rootLayout: string | undefined\n  let globalError: string = defaultGlobalErrorPath\n  let globalNotFound: string = defaultNotFoundPath\n\n  async function resolveAdjacentParallelSegments(\n    segmentPath: string\n  ): Promise<string[]> {\n    const absoluteSegmentPath = await resolveDir(\n      `${appDirPrefix}${segmentPath}`\n    )\n\n    if (!absoluteSegmentPath) {\n      return []\n    }\n\n    const segmentIsDirectory = await isDirectory(absoluteSegmentPath)\n\n    if (!segmentIsDirectory) {\n      return []\n    }\n\n    // We need to resolve all parallel routes in this level.\n    const files = await fs.opendir(absoluteSegmentPath)\n\n    const parallelSegments: string[] = ['children']\n\n    for await (const dirent of files) {\n      // Make sure name starts with \"@\" and is a directory.\n      if (dirent.isDirectory() && dirent.name.charCodeAt(0) === 64) {\n        parallelSegments.push(dirent.name)\n      }\n    }\n\n    return parallelSegments\n  }\n\n  async function createSubtreePropsFromSegmentPath(\n    segments: string[],\n    nestedCollectedDeclarations: [string, string][]\n  ): Promise<{\n    treeCode: string\n  }> {\n    const segmentPath = segments.join('/')\n\n    // Existing tree are the children of the current segment\n    const props: Record<string, string> = {}\n    // Root layer could be 1st layer of normal routes\n    const isRootLayer = segments.length === 0\n    const isRootLayoutOrRootPage = segments.length <= 1\n\n    // We need to resolve all parallel routes in this level.\n    const parallelSegments: [key: string, segment: string | string[]][] = []\n    if (isRootLayer) {\n      parallelSegments.push(['children', ''])\n    } else {\n      parallelSegments.push(...resolveParallelSegments(segmentPath))\n    }\n\n    let metadata: Awaited<ReturnType<typeof createStaticMetadataFromRoute>> =\n      null\n    const routerDirPath = `${appDirPrefix}${segmentPath}`\n    const resolvedRouteDir = resolveDir(routerDirPath)\n\n    if (resolvedRouteDir) {\n      metadata = await createStaticMetadataFromRoute(resolvedRouteDir, {\n        basePath,\n        segment: segmentPath,\n        metadataResolver,\n        isRootLayoutOrRootPage,\n        pageExtensions,\n      })\n    }\n\n    for (const [parallelKey, parallelSegment] of parallelSegments) {\n      // if parallelSegment is the page segment (ie, `page$` and not ['page$']), it gets loaded into the __PAGE__ slot\n      // as it's the page for the current route.\n      if (parallelSegment === PAGE_SEGMENT) {\n        const matchedPagePath = `${appDirPrefix}${segmentPath}${\n          parallelKey === 'children' ? '' : `/${parallelKey}`\n        }/page`\n\n        const resolvedPagePath = await resolver(matchedPagePath)\n        if (resolvedPagePath) {\n          pages.push(resolvedPagePath)\n\n          const varName = `page${nestedCollectedDeclarations.length}`\n          nestedCollectedDeclarations.push([varName, resolvedPagePath])\n\n          // Use '' for segment as it's the page. There can't be a segment called '' so this is the safest way to add it.\n          props[normalizeParallelKey(parallelKey)] =\n            `['${PAGE_SEGMENT_KEY}', {}, {\n          page: [${varName}, ${JSON.stringify(resolvedPagePath)}],\n          ${createMetadataExportsCode(metadata)}\n        }]`\n          continue\n        } else {\n          throw new Error(`Can't resolve ${matchedPagePath}`)\n        }\n      }\n\n      // if the parallelSegment was not matched to the __PAGE__ slot, then it's a parallel route at this level.\n      // the code below recursively traverses the parallel slots directory to match the corresponding __PAGE__ for each parallel slot\n      // while also filling in layout/default/etc files into the loader tree at each segment level.\n\n      const subSegmentPath = [...segments]\n      if (parallelKey !== 'children') {\n        // A `children` parallel key should have already been processed in the above segment\n        // So we exclude it when constructing the subsegment path for the remaining segment levels\n        subSegmentPath.push(parallelKey)\n      }\n\n      const normalizedParallelSegment = Array.isArray(parallelSegment)\n        ? parallelSegment[0]\n        : parallelSegment\n\n      if (\n        normalizedParallelSegment !== PAGE_SEGMENT &&\n        normalizedParallelSegment !== PARALLEL_CHILDREN_SEGMENT\n      ) {\n        // If we don't have a page segment, nor a special $children marker, it means we need to traverse the next directory\n        // (ie, `normalizedParallelSegment` would correspond with the folder that contains the next level of pages/layout/etc)\n        // we push it to the subSegmentPath so that we can fill in the loader tree for that segment.\n        subSegmentPath.push(normalizedParallelSegment)\n      }\n\n      const parallelSegmentPath = subSegmentPath.join('/')\n\n      // Fill in the loader tree for all of the special files types (layout, default, etc) at this level\n      // `page` is not included here as it's added above.\n      const filePaths = await Promise.all(\n        Object.values(FILE_TYPES).map(async (file) => {\n          return [\n            file,\n            await resolver(\n              `${appDirPrefix}${\n                // TODO-APP: parallelSegmentPath sometimes ends in `/` but sometimes it doesn't. This should be consistent.\n                parallelSegmentPath.endsWith('/')\n                  ? parallelSegmentPath\n                  : parallelSegmentPath + '/'\n              }${file}`\n            ),\n          ] as const\n        })\n      )\n\n      // Only resolve global-* convention files at the root layer\n      if (isRootLayer) {\n        const resolvedGlobalErrorPath = await resolver(\n          `${appDirPrefix}/${GLOBAL_ERROR_FILE_TYPE}`\n        )\n        if (resolvedGlobalErrorPath) {\n          globalError = resolvedGlobalErrorPath\n        }\n        // Add global-error to root layer's filePaths, so that it's always available,\n        // by default it's the built-in global-error.js\n        filePaths.push([GLOBAL_ERROR_FILE_TYPE, globalError])\n\n        // TODO(global-not-found): remove this flag assertion condition\n        //  once global-not-found is stable\n        if (isGlobalNotFoundEnabled) {\n          const resolvedGlobalNotFoundPath = await resolver(\n            `${appDirPrefix}/${GLOBAL_NOT_FOUND_FILE_TYPE}`\n          )\n          if (resolvedGlobalNotFoundPath) {\n            globalNotFound = resolvedGlobalNotFoundPath\n          }\n          // Add global-not-found to root layer's filePaths, so that it's always available,\n          // by default it's the built-in global-not-found.js\n          filePaths.push([GLOBAL_NOT_FOUND_FILE_TYPE, globalNotFound])\n        }\n      }\n\n      let definedFilePaths = filePaths.filter(\n        ([, filePath]) => filePath !== undefined\n      ) as [ValueOf<typeof FILE_TYPES>, string][]\n\n      // Add default access fallback as root fallback if not present\n      const existedConventionNames = new Set(\n        definedFilePaths.map(([type]) => type)\n      )\n      // If the first layer is a group route, we treat it as root layer\n      const isFirstLayerGroupRoute =\n        segments.length === 1 &&\n        subSegmentPath.filter((seg) => isGroupSegment(seg)).length === 1\n\n      if (isRootLayer || isFirstLayerGroupRoute) {\n        const accessFallbackTypes = Object.keys(\n          defaultHTTPAccessFallbackPaths\n        ) as (keyof typeof defaultHTTPAccessFallbackPaths)[]\n        for (const type of accessFallbackTypes) {\n          const hasRootFallbackFile = await resolver(\n            `${appDirPrefix}/${FILE_TYPES[type]}`\n          )\n          const hasLayerFallbackFile = existedConventionNames.has(type)\n\n          // If you already have a root access error fallback, don't insert default access error boundary to group routes root\n          if (\n            // Is treated as root layout and without boundary\n            !(hasRootFallbackFile && isFirstLayerGroupRoute) &&\n            // Does not have a fallback boundary file\n            !hasLayerFallbackFile\n          ) {\n            const defaultFallbackPath = defaultHTTPAccessFallbackPaths[type]\n            if (!(isDefaultNotFound && type === 'not-found')) {\n              definedFilePaths.push([type, defaultFallbackPath])\n            }\n          }\n        }\n      }\n\n      if (!rootLayout) {\n        const layoutPath = definedFilePaths.find(\n          ([type]) => type === 'layout'\n        )?.[1]\n        rootLayout = layoutPath\n\n        // When `global-not-found` is disabled, we insert a default layout if\n        // root layout is presented. This logic and the default layout will be removed\n        // once `global-not-found` is stabilized.\n        if (\n          !isGlobalNotFoundEnabled &&\n          isDefaultNotFound &&\n          !layoutPath &&\n          !rootLayout\n        ) {\n          rootLayout = defaultLayoutPath\n          definedFilePaths.push(['layout', rootLayout])\n        }\n      }\n\n      let parallelSegmentKey = Array.isArray(parallelSegment)\n        ? parallelSegment[0]\n        : parallelSegment\n\n      // normalize the parallel segment key to remove any special markers that we inserted in the\n      // earlier logic (such as children$ and page$). These should never appear in the loader tree, and\n      // should instead be the corresponding segment keys (ie `__PAGE__`) or the `children` parallel route.\n      parallelSegmentKey =\n        parallelSegmentKey === PARALLEL_CHILDREN_SEGMENT\n          ? 'children'\n          : parallelSegmentKey === PAGE_SEGMENT\n            ? PAGE_SEGMENT_KEY\n            : parallelSegmentKey\n\n      const normalizedParallelKey = normalizeParallelKey(parallelKey)\n      let subtreeCode\n      // If it's root not found page, set not-found boundary as children page\n      if (isNotFoundRoute) {\n        if (normalizedParallelKey === 'children') {\n          const matchedGlobalNotFound = isGlobalNotFoundEnabled\n            ? definedFilePaths.find(\n                ([type]) => type === GLOBAL_NOT_FOUND_FILE_TYPE\n              )?.[1] ?? defaultGlobalNotFoundPath\n            : undefined\n\n          // If custom global-not-found.js is defined, use global-not-found.js\n          if (matchedGlobalNotFound) {\n            const varName = `notFound${nestedCollectedDeclarations.length}`\n            nestedCollectedDeclarations.push([varName, matchedGlobalNotFound])\n            subtreeCode = `{\n              children: [${JSON.stringify(UNDERSCORE_NOT_FOUND_ROUTE)}, {\n                children: ['${PAGE_SEGMENT_KEY}', {}, {\n                  page: [\n                    ${varName},\n                    ${JSON.stringify(matchedGlobalNotFound)}\n                  ]\n                }]\n              }, {}]\n            }`\n          } else {\n            // If custom not-found.js is found, use it and layout to compose the page,\n            // and fallback to built-in not-found component if doesn't exist.\n            const notFoundPath =\n              definedFilePaths.find(([type]) => type === 'not-found')?.[1] ??\n              defaultNotFoundPath\n            const varName = `notFound${nestedCollectedDeclarations.length}`\n            nestedCollectedDeclarations.push([varName, notFoundPath])\n            subtreeCode = `{\n              children: [${JSON.stringify(UNDERSCORE_NOT_FOUND_ROUTE)}, {\n                children: ['${PAGE_SEGMENT_KEY}', {}, {\n                  page: [\n                    ${varName},\n                    ${JSON.stringify(notFoundPath)}\n                  ]\n                }]\n              }, {}]\n            }`\n          }\n        }\n      }\n\n      // For 404 route\n      // if global-not-found is in definedFilePaths, remove root layout for /_not-found\n      // TODO: remove this once global-not-found is stable.\n      if (isNotFoundRoute && isGlobalNotFoundEnabled) {\n        definedFilePaths = definedFilePaths.filter(\n          ([type]) => type !== 'layout'\n        )\n      }\n\n      const modulesCode = `{\n        ${definedFilePaths\n          .map(([file, filePath]) => {\n            const varName = `module${nestedCollectedDeclarations.length}`\n            nestedCollectedDeclarations.push([varName, filePath])\n            return `'${file}': [${varName}, ${JSON.stringify(filePath)}],`\n          })\n          .join('\\n')}\n        ${createMetadataExportsCode(metadata)}\n      }`\n\n      if (!subtreeCode) {\n        const { treeCode: pageSubtreeCode } =\n          await createSubtreePropsFromSegmentPath(\n            subSegmentPath,\n            nestedCollectedDeclarations\n          )\n\n        subtreeCode = pageSubtreeCode\n      }\n\n      props[normalizedParallelKey] = `[\n        '${parallelSegmentKey}',\n        ${subtreeCode},\n        ${modulesCode}\n      ]`\n    }\n\n    const adjacentParallelSegments =\n      await resolveAdjacentParallelSegments(segmentPath)\n\n    for (const adjacentParallelSegment of adjacentParallelSegments) {\n      if (!props[normalizeParallelKey(adjacentParallelSegment)]) {\n        const actualSegment =\n          adjacentParallelSegment === 'children'\n            ? ''\n            : `/${adjacentParallelSegment}`\n\n        // if a default is found, use that. Otherwise use the fallback, which will trigger a `notFound()`\n        const defaultPath =\n          (await resolver(\n            `${appDirPrefix}${segmentPath}${actualSegment}/default`\n          )) ?? PARALLEL_ROUTE_DEFAULT_PATH\n\n        const varName = `default${nestedCollectedDeclarations.length}`\n        nestedCollectedDeclarations.push([varName, defaultPath])\n        props[normalizeParallelKey(adjacentParallelSegment)] = `[\n          '${DEFAULT_SEGMENT_KEY}',\n          {},\n          {\n            defaultPage: [${varName}, ${JSON.stringify(defaultPath)}],\n          }\n        ]`\n      }\n    }\n    return {\n      treeCode: `{\n        ${Object.entries(props)\n          .map(([key, value]) => `${key}: ${value}`)\n          .join(',\\n')}\n      }`,\n    }\n  }\n\n  const { treeCode } = await createSubtreePropsFromSegmentPath(\n    [],\n    collectedDeclarations\n  )\n\n  return {\n    treeCode: `${treeCode}.children;`,\n    pages: `${JSON.stringify(pages)};`,\n    rootLayout,\n    globalError,\n    globalNotFound,\n  }\n}\n\nfunction createAbsolutePath(appDir: string, pathToTurnAbsolute: string) {\n  return (\n    pathToTurnAbsolute\n      // Replace all POSIX path separators with the current OS path separator\n      .replace(/\\//g, path.sep)\n      .replace(/^private-next-app-dir/, appDir)\n  )\n}\n\nconst filesInDirMapMap: WeakMap<\n  Compilation,\n  Map<string, Promise<Set<string>>>\n> = new WeakMap()\nconst nextAppLoader: AppLoader = async function nextAppLoader() {\n  const loaderOptions = this.getOptions()\n  const {\n    name,\n    appDir,\n    appPaths,\n    pagePath,\n    pageExtensions,\n    rootDir,\n    tsconfigPath,\n    isDev,\n    nextConfigOutput,\n    preferredRegion,\n    basePath,\n    middlewareConfig: middlewareConfigBase64,\n  } = loaderOptions\n\n  const isGlobalNotFoundEnabled = !!loaderOptions.isGlobalNotFoundEnabled\n\n  // Update FILE_TYPES on the very top-level of the loader\n  if (!isGlobalNotFoundEnabled) {\n    // @ts-expect-error this delete is only necessary while experimental\n    delete FILE_TYPES['global-not-found']\n  }\n\n  const buildInfo = getModuleBuildInfo((this as any)._module)\n  const collectedDeclarations: [string, string][] = []\n  const page = name.replace(/^app/, '')\n  const middlewareConfig: MiddlewareConfig = JSON.parse(\n    Buffer.from(middlewareConfigBase64, 'base64').toString()\n  )\n  buildInfo.route = {\n    page,\n    absolutePagePath: createAbsolutePath(appDir, pagePath),\n    preferredRegion,\n    middlewareConfig,\n    relatedModules: [],\n  }\n\n  const extensions =\n    typeof pageExtensions === 'string'\n      ? [pageExtensions]\n      : pageExtensions.map((extension) => `.${extension}`)\n\n  const normalizedAppPaths =\n    typeof appPaths === 'string' ? [appPaths] : appPaths || []\n\n  const resolveParallelSegments = (\n    pathname: string\n  ): [string, string | string[]][] => {\n    const matched: Record<string, string | string[]> = {}\n    let existingChildrenPath: string | undefined\n    for (const appPath of normalizedAppPaths) {\n      if (appPath.startsWith(pathname + '/')) {\n        const rest = appPath.slice(pathname.length + 1).split('/')\n\n        // It is the actual page, mark it specially.\n        if (rest.length === 1 && rest[0] === 'page') {\n          existingChildrenPath = appPath\n          matched.children = PAGE_SEGMENT\n          continue\n        }\n\n        const isParallelRoute = rest[0].startsWith('@')\n        if (isParallelRoute) {\n          if (rest.length === 2 && rest[1] === 'page') {\n            // We found a parallel route at this level. We don't want to mark it explicitly as the page segment,\n            // as that should be matched to the `children` slot. Instead, we use an array, to signal to `createSubtreePropsFromSegmentPath`\n            // that it needs to recursively fill in the loader tree code for the parallel route at the appropriate levels.\n            matched[rest[0]] = [PAGE_SEGMENT]\n            continue\n          }\n          // If it was a parallel route but we weren't able to find the page segment (ie, maybe the page is nested further)\n          // we first insert a special marker to ensure that we still process layout/default/etc at the slot level prior to continuing\n          // on to the page segment.\n          matched[rest[0]] = [PARALLEL_CHILDREN_SEGMENT, ...rest.slice(1)]\n          continue\n        }\n\n        if (existingChildrenPath && matched.children !== rest[0]) {\n          // If we get here, it means we already set a `page` segment earlier in the loop,\n          // meaning we already matched a page to the `children` parallel segment.\n          const isIncomingParallelPage = appPath.includes('@')\n          const hasCurrentParallelPage = existingChildrenPath.includes('@')\n\n          if (isIncomingParallelPage) {\n            // The duplicate segment was for a parallel slot. In this case,\n            // rather than throwing an error, we can ignore it since this can happen for valid reasons.\n            // For example, when we attempt to normalize catch-all routes, we'll push potential slot matches so\n            // that they are available in the loader tree when we go to render the page.\n            // We only need to throw an error if the duplicate segment was for a regular page.\n            // For example, /app/(groupa)/page & /app/(groupb)/page is an error since it corresponds\n            // with the same path.\n            continue\n          } else if (!hasCurrentParallelPage && !isIncomingParallelPage) {\n            // Both the current `children` and the incoming `children` are regular pages.\n            throw new Error(\n              `You cannot have two parallel pages that resolve to the same path. Please check ${existingChildrenPath} and ${appPath}. Refer to the route group docs for more information: https://nextjs.org/docs/app/building-your-application/routing/route-groups`\n            )\n          }\n        }\n\n        existingChildrenPath = appPath\n        matched.children = rest[0]\n      }\n    }\n\n    return Object.entries(matched)\n  }\n\n  const resolveDir: DirResolver = (pathToResolve) => {\n    return createAbsolutePath(appDir, pathToResolve)\n  }\n\n  const resolveAppRoute: PathResolver = (pathToResolve) => {\n    return createAbsolutePath(appDir, pathToResolve)\n  }\n\n  // Cached checker to see if a file exists in a given directory.\n  // This can be more efficient than checking them with `fs.stat` one by one\n  // because all the thousands of files are likely in a few possible directories.\n  // Note that it should only be cached for this compilation, not globally.\n  const fileExistsInDirectory = async (dirname: string, fileName: string) => {\n    // I don't think we should ever hit this code path, but if we do we should handle it gracefully.\n    if (this._compilation === undefined) {\n      try {\n        return (await getFilesInDir(dirname).catch(() => new Set())).has(\n          fileName\n        )\n      } catch (e) {\n        return false\n      }\n    }\n    const map =\n      filesInDirMapMap.get(this._compilation) ||\n      new Map<string, Promise<Set<string>>>()\n    if (!filesInDirMapMap.has(this._compilation)) {\n      filesInDirMapMap.set(this._compilation, map)\n    }\n    if (!map.has(dirname)) {\n      map.set(\n        dirname,\n        getFilesInDir(dirname).catch(() => new Set())\n      )\n    }\n    return ((await map.get(dirname)) || new Set()).has(fileName)\n  }\n\n  const resolver: PathResolver = async (pathname) => {\n    const absolutePath = createAbsolutePath(appDir, pathname)\n\n    const filenameIndex = absolutePath.lastIndexOf(path.sep)\n    const dirname = absolutePath.slice(0, filenameIndex)\n    const filename = absolutePath.slice(filenameIndex + 1)\n\n    let result: string | undefined\n\n    for (const ext of extensions) {\n      const absolutePathWithExtension = `${absolutePath}${ext}`\n      if (\n        !result &&\n        (await fileExistsInDirectory(dirname, `${filename}${ext}`))\n      ) {\n        result = absolutePathWithExtension\n      }\n      // Call `addMissingDependency` for all files even if they didn't match,\n      // because they might be added or removed during development.\n      this.addMissingDependency(absolutePathWithExtension)\n    }\n\n    return result\n  }\n\n  const metadataResolver: MetadataResolver = async (\n    dirname,\n    filename,\n    exts\n  ) => {\n    const absoluteDir = createAbsolutePath(appDir, dirname)\n\n    let result: string | undefined\n\n    for (const ext of exts) {\n      // Compared to `resolver` above the exts do not have the `.` included already, so it's added here.\n      const filenameWithExt = `${filename}.${ext}`\n      const absolutePathWithExtension = `${absoluteDir}${path.sep}${filenameWithExt}`\n      if (!result && (await fileExistsInDirectory(dirname, filenameWithExt))) {\n        result = absolutePathWithExtension\n      }\n      // Call `addMissingDependency` for all files even if they didn't match,\n      // because they might be added or removed during development.\n      this.addMissingDependency(absolutePathWithExtension)\n    }\n\n    return result\n  }\n\n  if (isAppRouteRoute(name)) {\n    return createAppRouteCode({\n      appDir,\n      // TODO: investigate if the local `page` is the same as the loaderOptions.page\n      page: loaderOptions.page,\n      name,\n      pagePath,\n      resolveAppRoute,\n      pageExtensions,\n      nextConfigOutput,\n    })\n  }\n\n  let treeCodeResult = await createTreeCodeFromPath(pagePath, {\n    page,\n    resolveDir,\n    resolver,\n    metadataResolver,\n    resolveParallelSegments,\n    loaderContext: this,\n    pageExtensions,\n    basePath,\n    collectedDeclarations,\n    isGlobalNotFoundEnabled,\n  })\n\n  const isGlobalNotFoundPath =\n    page === UNDERSCORE_NOT_FOUND_ROUTE_ENTRY &&\n    !!treeCodeResult.globalNotFound &&\n    isGlobalNotFoundEnabled\n\n  if (!treeCodeResult.rootLayout && !isGlobalNotFoundPath) {\n    if (!isDev) {\n      // If we're building and missing a root layout, exit the build\n      Log.error(\n        `${bold(\n          pagePath.replace(`${APP_DIR_ALIAS}/`, '')\n        )} doesn't have a root layout. To fix this error, make sure every page has a root layout.`\n      )\n      process.exit(1)\n    } else {\n      // In dev we'll try to create a root layout\n      const [createdRootLayout, rootLayoutPath] = await verifyRootLayout({\n        appDir: appDir,\n        dir: rootDir!,\n        tsconfigPath: tsconfigPath!,\n        pagePath,\n        pageExtensions,\n      })\n      if (!createdRootLayout) {\n        let message = `${bold(\n          pagePath.replace(`${APP_DIR_ALIAS}/`, '')\n        )} doesn't have a root layout. `\n\n        if (rootLayoutPath) {\n          message += `We tried to create ${bold(\n            path.relative(this._compiler?.context ?? '', rootLayoutPath)\n          )} for you but something went wrong.`\n        } else {\n          message +=\n            'To fix this error, make sure every page has a root layout.'\n        }\n\n        throw new Error(message)\n      }\n\n      // Clear fs cache, get the new result with the created root layout.\n      if (this._compilation) filesInDirMapMap.get(this._compilation)?.clear()\n      treeCodeResult = await createTreeCodeFromPath(pagePath, {\n        page,\n        resolveDir,\n        resolver,\n        metadataResolver,\n        resolveParallelSegments,\n        loaderContext: this,\n        pageExtensions,\n        basePath,\n        collectedDeclarations,\n        isGlobalNotFoundEnabled,\n      })\n    }\n  }\n\n  const pathname = new AppPathnameNormalizer().normalize(page)\n\n  // Prefer to modify next/src/server/app-render/entry-base.ts since this is shared with Turbopack.\n  // Any changes to this code should be reflected in Turbopack's app_source.rs and/or app-renderer.tsx as well.\n  const code = await loadEntrypoint(\n    'app-page',\n    {\n      VAR_DEFINITION_PAGE: page,\n      VAR_DEFINITION_PATHNAME: pathname,\n      VAR_MODULE_GLOBAL_ERROR: treeCodeResult.globalError,\n    },\n    {\n      tree: treeCodeResult.treeCode,\n      pages: treeCodeResult.pages,\n      __next_app_require__: '__webpack_require__',\n      // all modules are in the entry chunk, so we never actually need to load chunks in webpack\n      __next_app_load_chunk__: '() => Promise.resolve()',\n    }\n  )\n\n  // Lazily evaluate the imported modules in the generated code\n  const header = collectedDeclarations\n    .map(([varName, modulePath]) => {\n      return `const ${varName} = () => import(/* webpackMode: \"eager\" */ ${JSON.stringify(\n        modulePath\n      )});\\n`\n    })\n    .join('')\n\n  return header + code\n}\n\nexport default nextAppLoader\n"], "names": ["HTTP_ACCESS_FALLBACKS", "forbidden", "unauthorized", "defaultHTTPAccessFallbackPaths", "FILE_TYPES", "layout", "template", "error", "loading", "GLOBAL_ERROR_FILE_TYPE", "GLOBAL_NOT_FOUND_FILE_TYPE", "PAGE_SEGMENT", "PARALLEL_CHILDREN_SEGMENT", "defaultGlobalErrorPath", "defaultNotFoundPath", "defaultLayoutPath", "defaultGlobalNotFoundPath", "normalizeP<PERSON><PERSON><PERSON><PERSON>ey", "key", "startsWith", "slice", "isDirectory", "pathname", "stat", "fs", "err", "createTreeCodeFromPath", "pagePath", "page", "resolveDir", "resolver", "resolveParallelSegments", "metadataResolver", "pageExtensions", "basePath", "collectedDeclarations", "isGlobalNotFoundEnabled", "splittedPath", "split", "isNotFoundRoute", "UNDERSCORE_NOT_FOUND_ROUTE_ENTRY", "isDefaultNotFound", "isAppBuiltinNotFoundPage", "appDirPrefix", "APP_DIR_ALIAS", "pages", "rootLayout", "globalError", "globalNotFound", "resolveAdjacentParallelSegments", "segmentPath", "absoluteSegmentPath", "segmentIsDirectory", "files", "opendir", "parallelSegments", "dirent", "name", "charCodeAt", "push", "createSubtreePropsFromSegmentPath", "segments", "nestedCollectedDeclarations", "join", "props", "isRootLayer", "length", "isRootLayoutOrRootPage", "metadata", "routerDirPath", "resolvedRouteDir", "createStaticMetadataFromRoute", "segment", "parallel<PERSON>ey", "parallelSegment", "matchedPagePath", "resolvedPagePath", "varName", "PAGE_SEGMENT_KEY", "JSON", "stringify", "createMetadataExportsCode", "Error", "subSegmentPath", "normalizedParallelSegment", "Array", "isArray", "parallelSegmentPath", "filePaths", "Promise", "all", "Object", "values", "map", "file", "endsWith", "resolvedGlobalErrorPath", "resolvedGlobalNotFoundPath", "definedFilePaths", "filter", "filePath", "undefined", "existedConventionNames", "Set", "type", "isFirstLayerGroupRoute", "seg", "isGroupSegment", "accessFallbackTypes", "keys", "hasRootFallbackFile", "hasLayerFallbackFile", "has", "defaultFallbackPath", "<PERSON><PERSON><PERSON>", "find", "parallelSegmentKey", "normalizedParallel<PERSON>ey", "subtreeCode", "matchedGlobalNotFound", "UNDERSCORE_NOT_FOUND_ROUTE", "notFoundPath", "modulesCode", "treeCode", "pageSubtreeCode", "adjacentParallelSegments", "adjacentParallelSegment", "actualSegment", "defaultPath", "PARALLEL_ROUTE_DEFAULT_PATH", "DEFAULT_SEGMENT_KEY", "entries", "value", "createAbsolutePath", "appDir", "pathToTurnAbsolute", "replace", "path", "sep", "filesInDirMapMap", "WeakMap", "nextApp<PERSON><PERSON>der", "loaderOptions", "getOptions", "appPaths", "rootDir", "tsconfigPath", "isDev", "nextConfigOutput", "preferredRegion", "middlewareConfig", "middlewareConfigBase64", "buildInfo", "getModuleBuildInfo", "_module", "parse", "<PERSON><PERSON><PERSON>", "from", "toString", "route", "absolutePagePath", "relatedModules", "extensions", "extension", "normalizedAppPaths", "matched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appPath", "rest", "children", "isParallelRoute", "isIncomingParallelPage", "includes", "hasCurrentParallelPage", "pathToResolve", "resolveAppRoute", "fileExistsInDirectory", "dirname", "fileName", "_compilation", "getFilesInDir", "catch", "e", "get", "Map", "set", "absolutePath", "filenameIndex", "lastIndexOf", "filename", "result", "ext", "absolutePathWithExtension", "addMissingDependency", "exts", "absoluteDir", "filenameWithExt", "isAppRouteRoute", "createAppRouteCode", "treeCodeResult", "loaderContext", "isGlobalNotFoundPath", "Log", "bold", "process", "exit", "createdRootLayout", "rootLayoutPath", "verifyRootLayout", "dir", "message", "relative", "_compiler", "context", "clear", "AppPathnameNormalizer", "normalize", "code", "loadEntrypoint", "VAR_DEFINITION_PAGE", "VAR_DEFINITION_PATHNAME", "VAR_MODULE_GLOBAL_ERROR", "tree", "__next_app_require__", "__next_app_load_chunk__", "header", "modulePath"], "mappings": ";;;;+BA+1BA;;;eAAA;;;2BA11BO;6DAGU;4BACI;oCACc;kCACF;6DACZ;4BACS;0BAIvB;oBACwB;iCACC;uCAEM;uBAEG;gCACV;yBAKxB;+BACuB;yBAEc;oCAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBnC,MAAMA,wBAAwB;IAC5B,aAAa;IACbC,WAAW;IACXC,cAAc;AAChB;AACA,MAAMC,iCAAiC;IACrC,aAAa;IACbF,WAAW;IACXC,cAAc;AAChB;AAEA,MAAME,aAAa;IACjBC,QAAQ;IACRC,UAAU;IACVC,OAAO;IACPC,SAAS;IACT,gBAAgB;IAChB,oBAAoB;IACpB,GAAGR,qBAAqB;AAC1B;AAEA,MAAMS,yBAAyB;AAC/B,MAAMC,6BAA6B;AACnC,MAAMC,eAAe;AACrB,MAAMC,4BAA4B;AAElC,MAAMC,yBACJ;AACF,MAAMC,sBAAsB;AAC5B,MAAMC,oBAAoB;AAC1B,MAAMC,4BACJ;AAsBF,MAAMC,uBAAuB,CAACC,MAC5BA,IAAIC,UAAU,CAAC,OAAOD,IAAIE,KAAK,CAAC,KAAKF;AAEvC,MAAMG,cAAc,OAAOC;IACzB,IAAI;QACF,MAAMC,OAAO,MAAMC,YAAE,CAACD,IAAI,CAACD;QAC3B,OAAOC,KAAKF,WAAW;IACzB,EAAE,OAAOI,KAAK;QACZ,OAAO;IACT;AACF;AAEA,eAAeC,uBACbC,QAAgB,EAChB,EACEC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,uBAAuB,EACvBC,gBAAgB,EAChBC,cAAc,EACdC,QAAQ,EACRC,qBAAqB,EACrBC,uBAAuB,EAcxB;IAQD,MAAMC,eAAeV,SAASW,KAAK,CAAC,SAAS;IAC7C,MAAMC,kBAAkBX,SAASY,2CAAgC;IACjE,MAAMC,oBAAoBC,IAAAA,+BAAwB,EAACf;IAEnD,MAAMgB,eAAeF,oBAAoBG,yBAAa,GAAGP,YAAY,CAAC,EAAE;IACxE,MAAMQ,QAAkB,EAAE;IAE1B,IAAIC;IACJ,IAAIC,cAAsBlC;IAC1B,IAAImC,iBAAyBlC;IAE7B,eAAemC,gCACbC,WAAmB;QAEnB,MAAMC,sBAAsB,MAAMtB,WAChC,GAAGc,eAAeO,aAAa;QAGjC,IAAI,CAACC,qBAAqB;YACxB,OAAO,EAAE;QACX;QAEA,MAAMC,qBAAqB,MAAM/B,YAAY8B;QAE7C,IAAI,CAACC,oBAAoB;YACvB,OAAO,EAAE;QACX;QAEA,wDAAwD;QACxD,MAAMC,QAAQ,MAAM7B,YAAE,CAAC8B,OAAO,CAACH;QAE/B,MAAMI,mBAA6B;YAAC;SAAW;QAE/C,WAAW,MAAMC,UAAUH,MAAO;YAChC,qDAAqD;YACrD,IAAIG,OAAOnC,WAAW,MAAMmC,OAAOC,IAAI,CAACC,UAAU,CAAC,OAAO,IAAI;gBAC5DH,iBAAiBI,IAAI,CAACH,OAAOC,IAAI;YACnC;QACF;QAEA,OAAOF;IACT;IAEA,eAAeK,kCACbC,QAAkB,EAClBC,2BAA+C;QAI/C,MAAMZ,cAAcW,SAASE,IAAI,CAAC;QAElC,wDAAwD;QACxD,MAAMC,QAAgC,CAAC;QACvC,iDAAiD;QACjD,MAAMC,cAAcJ,SAASK,MAAM,KAAK;QACxC,MAAMC,yBAAyBN,SAASK,MAAM,IAAI;QAElD,wDAAwD;QACxD,MAAMX,mBAAgE,EAAE;QACxE,IAAIU,aAAa;YACfV,iBAAiBI,IAAI,CAAC;gBAAC;gBAAY;aAAG;QACxC,OAAO;YACLJ,iBAAiBI,IAAI,IAAI5B,wBAAwBmB;QACnD;QAEA,IAAIkB,WACF;QACF,MAAMC,gBAAgB,GAAG1B,eAAeO,aAAa;QACrD,MAAMoB,mBAAmBzC,WAAWwC;QAEpC,IAAIC,kBAAkB;YACpBF,WAAW,MAAMG,IAAAA,uCAA6B,EAACD,kBAAkB;gBAC/DpC;gBACAsC,SAAStB;gBACTlB;gBACAmC;gBACAlC;YACF;QACF;QAEA,KAAK,MAAM,CAACwC,aAAaC,gBAAgB,IAAInB,iBAAkB;YAC7D,gHAAgH;YAChH,0CAA0C;YAC1C,IAAImB,oBAAoB/D,cAAc;gBACpC,MAAMgE,kBAAkB,GAAGhC,eAAeO,cACxCuB,gBAAgB,aAAa,KAAK,CAAC,CAAC,EAAEA,aAAa,CACpD,KAAK,CAAC;gBAEP,MAAMG,mBAAmB,MAAM9C,SAAS6C;gBACxC,IAAIC,kBAAkB;oBACpB/B,MAAMc,IAAI,CAACiB;oBAEX,MAAMC,UAAU,CAAC,IAAI,EAAEf,4BAA4BI,MAAM,EAAE;oBAC3DJ,4BAA4BH,IAAI,CAAC;wBAACkB;wBAASD;qBAAiB;oBAE5D,+GAA+G;oBAC/GZ,KAAK,CAAC/C,qBAAqBwD,aAAa,GACtC,CAAC,EAAE,EAAEK,yBAAgB,CAAC;iBACjB,EAAED,QAAQ,EAAE,EAAEE,KAAKC,SAAS,CAACJ,kBAAkB;UACtD,EAAEK,IAAAA,mCAAyB,EAACb,UAAU;UACtC,CAAC;oBACD;gBACF,OAAO;oBACL,MAAM,qBAA6C,CAA7C,IAAIc,MAAM,CAAC,cAAc,EAAEP,iBAAiB,GAA5C,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4C;gBACpD;YACF;YAEA,yGAAyG;YACzG,+HAA+H;YAC/H,6FAA6F;YAE7F,MAAMQ,iBAAiB;mBAAItB;aAAS;YACpC,IAAIY,gBAAgB,YAAY;gBAC9B,oFAAoF;gBACpF,0FAA0F;gBAC1FU,eAAexB,IAAI,CAACc;YACtB;YAEA,MAAMW,4BAA4BC,MAAMC,OAAO,CAACZ,mBAC5CA,eAAe,CAAC,EAAE,GAClBA;YAEJ,IACEU,8BAA8BzE,gBAC9ByE,8BAA8BxE,2BAC9B;gBACA,mHAAmH;gBACnH,sHAAsH;gBACtH,4FAA4F;gBAC5FuE,eAAexB,IAAI,CAACyB;YACtB;YAEA,MAAMG,sBAAsBJ,eAAepB,IAAI,CAAC;YAEhD,kGAAkG;YAClG,mDAAmD;YACnD,MAAMyB,YAAY,MAAMC,QAAQC,GAAG,CACjCC,OAAOC,MAAM,CAACxF,YAAYyF,GAAG,CAAC,OAAOC;gBACnC,OAAO;oBACLA;oBACA,MAAMhE,SACJ,GAAGa,eACD,2GAA2G;oBAC3G4C,oBAAoBQ,QAAQ,CAAC,OACzBR,sBACAA,sBAAsB,MACzBO,MAAM;iBAEZ;YACH;YAGF,2DAA2D;YAC3D,IAAI7B,aAAa;gBACf,MAAM+B,0BAA0B,MAAMlE,SACpC,GAAGa,aAAa,CAAC,EAAElC,wBAAwB;gBAE7C,IAAIuF,yBAAyB;oBAC3BjD,cAAciD;gBAChB;gBACA,6EAA6E;gBAC7E,+CAA+C;gBAC/CR,UAAU7B,IAAI,CAAC;oBAAClD;oBAAwBsC;iBAAY;gBAEpD,+DAA+D;gBAC/D,mCAAmC;gBACnC,IAAIX,yBAAyB;oBAC3B,MAAM6D,6BAA6B,MAAMnE,SACvC,GAAGa,aAAa,CAAC,EAAEjC,4BAA4B;oBAEjD,IAAIuF,4BAA4B;wBAC9BjD,iBAAiBiD;oBACnB;oBACA,iFAAiF;oBACjF,mDAAmD;oBACnDT,UAAU7B,IAAI,CAAC;wBAACjD;wBAA4BsC;qBAAe;gBAC7D;YACF;YAEA,IAAIkD,mBAAmBV,UAAUW,MAAM,CACrC,CAAC,GAAGC,SAAS,GAAKA,aAAaC;YAGjC,8DAA8D;YAC9D,MAAMC,yBAAyB,IAAIC,IACjCL,iBAAiBL,GAAG,CAAC,CAAC,CAACW,KAAK,GAAKA;YAEnC,iEAAiE;YACjE,MAAMC,yBACJ5C,SAASK,MAAM,KAAK,KACpBiB,eAAegB,MAAM,CAAC,CAACO,MAAQC,IAAAA,uBAAc,EAACD,MAAMxC,MAAM,KAAK;YAEjE,IAAID,eAAewC,wBAAwB;gBACzC,MAAMG,sBAAsBjB,OAAOkB,IAAI,CACrC1G;gBAEF,KAAK,MAAMqG,QAAQI,oBAAqB;oBACtC,MAAME,sBAAsB,MAAMhF,SAChC,GAAGa,aAAa,CAAC,EAAEvC,UAAU,CAACoG,KAAK,EAAE;oBAEvC,MAAMO,uBAAuBT,uBAAuBU,GAAG,CAACR;oBAExD,oHAAoH;oBACpH,IACE,iDAAiD;oBACjD,CAAEM,CAAAA,uBAAuBL,sBAAqB,KAC9C,yCAAyC;oBACzC,CAACM,sBACD;wBACA,MAAME,sBAAsB9G,8BAA8B,CAACqG,KAAK;wBAChE,IAAI,CAAE/D,CAAAA,qBAAqB+D,SAAS,WAAU,GAAI;4BAChDN,iBAAiBvC,IAAI,CAAC;gCAAC6C;gCAAMS;6BAAoB;wBACnD;oBACF;gBACF;YACF;YAEA,IAAI,CAACnE,YAAY;oBACIoD;gBAAnB,MAAMgB,cAAahB,yBAAAA,iBAAiBiB,IAAI,CACtC,CAAC,CAACX,KAAK,GAAKA,SAAS,8BADJN,sBAEhB,CAAC,EAAE;gBACNpD,aAAaoE;gBAEb,qEAAqE;gBACrE,8EAA8E;gBAC9E,yCAAyC;gBACzC,IACE,CAAC9E,2BACDK,qBACA,CAACyE,cACD,CAACpE,YACD;oBACAA,aAAa/B;oBACbmF,iBAAiBvC,IAAI,CAAC;wBAAC;wBAAUb;qBAAW;gBAC9C;YACF;YAEA,IAAIsE,qBAAqB/B,MAAMC,OAAO,CAACZ,mBACnCA,eAAe,CAAC,EAAE,GAClBA;YAEJ,2FAA2F;YAC3F,iGAAiG;YACjG,qGAAqG;YACrG0C,qBACEA,uBAAuBxG,4BACnB,aACAwG,uBAAuBzG,eACrBmE,yBAAgB,GAChBsC;YAER,MAAMC,wBAAwBpG,qBAAqBwD;YACnD,IAAI6C;YACJ,uEAAuE;YACvE,IAAI/E,iBAAiB;gBACnB,IAAI8E,0BAA0B,YAAY;wBAEpCnB;oBADJ,MAAMqB,wBAAwBnF,0BAC1B8D,EAAAA,0BAAAA,iBAAiBiB,IAAI,CACnB,CAAC,CAACX,KAAK,GAAKA,SAAS9F,gDADvBwF,uBAEG,CAAC,EAAE,KAAIlF,4BACVqF;oBAEJ,oEAAoE;oBACpE,IAAIkB,uBAAuB;wBACzB,MAAM1C,UAAU,CAAC,QAAQ,EAAEf,4BAA4BI,MAAM,EAAE;wBAC/DJ,4BAA4BH,IAAI,CAAC;4BAACkB;4BAAS0C;yBAAsB;wBACjED,cAAc,CAAC;yBACF,EAAEvC,KAAKC,SAAS,CAACwC,qCAA0B,EAAE;4BAC1C,EAAE1C,yBAAgB,CAAC;;oBAE3B,EAAED,QAAQ;oBACV,EAAEE,KAAKC,SAAS,CAACuC,uBAAuB;;;;aAI/C,CAAC;oBACJ,OAAO;4BAIHrB;wBAHF,0EAA0E;wBAC1E,iEAAiE;wBACjE,MAAMuB,eACJvB,EAAAA,0BAAAA,iBAAiBiB,IAAI,CAAC,CAAC,CAACX,KAAK,GAAKA,SAAS,iCAA3CN,uBAAyD,CAAC,EAAE,KAC5DpF;wBACF,MAAM+D,UAAU,CAAC,QAAQ,EAAEf,4BAA4BI,MAAM,EAAE;wBAC/DJ,4BAA4BH,IAAI,CAAC;4BAACkB;4BAAS4C;yBAAa;wBACxDH,cAAc,CAAC;yBACF,EAAEvC,KAAKC,SAAS,CAACwC,qCAA0B,EAAE;4BAC1C,EAAE1C,yBAAgB,CAAC;;oBAE3B,EAAED,QAAQ;oBACV,EAAEE,KAAKC,SAAS,CAACyC,cAAc;;;;aAItC,CAAC;oBACJ;gBACF;YACF;YAEA,gBAAgB;YAChB,iFAAiF;YACjF,qDAAqD;YACrD,IAAIlF,mBAAmBH,yBAAyB;gBAC9C8D,mBAAmBA,iBAAiBC,MAAM,CACxC,CAAC,CAACK,KAAK,GAAKA,SAAS;YAEzB;YAEA,MAAMkB,cAAc,CAAC;QACnB,EAAExB,iBACCL,GAAG,CAAC,CAAC,CAACC,MAAMM,SAAS;gBACpB,MAAMvB,UAAU,CAAC,MAAM,EAAEf,4BAA4BI,MAAM,EAAE;gBAC7DJ,4BAA4BH,IAAI,CAAC;oBAACkB;oBAASuB;iBAAS;gBACpD,OAAO,CAAC,CAAC,EAAEN,KAAK,IAAI,EAAEjB,QAAQ,EAAE,EAAEE,KAAKC,SAAS,CAACoB,UAAU,EAAE,CAAC;YAChE,GACCrC,IAAI,CAAC,MAAM;QACd,EAAEkB,IAAAA,mCAAyB,EAACb,UAAU;OACvC,CAAC;YAEF,IAAI,CAACkD,aAAa;gBAChB,MAAM,EAAEK,UAAUC,eAAe,EAAE,GACjC,MAAMhE,kCACJuB,gBACArB;gBAGJwD,cAAcM;YAChB;YAEA5D,KAAK,CAACqD,sBAAsB,GAAG,CAAC;SAC7B,EAAED,mBAAmB;QACtB,EAAEE,YAAY;QACd,EAAEI,YAAY;OACf,CAAC;QACJ;QAEA,MAAMG,2BACJ,MAAM5E,gCAAgCC;QAExC,KAAK,MAAM4E,2BAA2BD,yBAA0B;YAC9D,IAAI,CAAC7D,KAAK,CAAC/C,qBAAqB6G,yBAAyB,EAAE;gBACzD,MAAMC,gBACJD,4BAA4B,aACxB,KACA,CAAC,CAAC,EAAEA,yBAAyB;gBAEnC,iGAAiG;gBACjG,MAAME,cACJ,AAAC,MAAMlG,SACL,GAAGa,eAAeO,cAAc6E,cAAc,QAAQ,CAAC,KACnDE,oCAA2B;gBAEnC,MAAMpD,UAAU,CAAC,OAAO,EAAEf,4BAA4BI,MAAM,EAAE;gBAC9DJ,4BAA4BH,IAAI,CAAC;oBAACkB;oBAASmD;iBAAY;gBACvDhE,KAAK,CAAC/C,qBAAqB6G,yBAAyB,GAAG,CAAC;WACrD,EAAEI,4BAAmB,CAAC;;;0BAGP,EAAErD,QAAQ,EAAE,EAAEE,KAAKC,SAAS,CAACgD,aAAa;;SAE3D,CAAC;YACJ;QACF;QACA,OAAO;YACLL,UAAU,CAAC;QACT,EAAEhC,OAAOwC,OAAO,CAACnE,OACd6B,GAAG,CAAC,CAAC,CAAC3E,KAAKkH,MAAM,GAAK,GAAGlH,IAAI,EAAE,EAAEkH,OAAO,EACxCrE,IAAI,CAAC,OAAO;OAChB,CAAC;QACJ;IACF;IAEA,MAAM,EAAE4D,QAAQ,EAAE,GAAG,MAAM/D,kCACzB,EAAE,EACFzB;IAGF,OAAO;QACLwF,UAAU,GAAGA,SAAS,UAAU,CAAC;QACjC9E,OAAO,GAAGkC,KAAKC,SAAS,CAACnC,OAAO,CAAC,CAAC;QAClCC;QACAC;QACAC;IACF;AACF;AAEA,SAASqF,mBAAmBC,MAAc,EAAEC,kBAA0B;IACpE,OACEA,kBACE,uEAAuE;KACtEC,OAAO,CAAC,OAAOC,aAAI,CAACC,GAAG,EACvBF,OAAO,CAAC,yBAAyBF;AAExC;AAEA,MAAMK,mBAGF,IAAIC;AACR,MAAMC,gBAA2B,eAAeA;IAC9C,MAAMC,gBAAgB,IAAI,CAACC,UAAU;IACrC,MAAM,EACJtF,IAAI,EACJ6E,MAAM,EACNU,QAAQ,EACRrH,QAAQ,EACRM,cAAc,EACdgH,OAAO,EACPC,YAAY,EACZC,KAAK,EACLC,gBAAgB,EAChBC,eAAe,EACfnH,QAAQ,EACRoH,kBAAkBC,sBAAsB,EACzC,GAAGT;IAEJ,MAAM1G,0BAA0B,CAAC,CAAC0G,cAAc1G,uBAAuB;IAEvE,wDAAwD;IACxD,IAAI,CAACA,yBAAyB;QAC5B,oEAAoE;QACpE,OAAOhC,UAAU,CAAC,mBAAmB;IACvC;IAEA,MAAMoJ,YAAYC,IAAAA,sCAAkB,EAAC,AAAC,IAAI,CAASC,OAAO;IAC1D,MAAMvH,wBAA4C,EAAE;IACpD,MAAMP,OAAO6B,KAAK+E,OAAO,CAAC,QAAQ;IAClC,MAAMc,mBAAqCvE,KAAK4E,KAAK,CACnDC,OAAOC,IAAI,CAACN,wBAAwB,UAAUO,QAAQ;IAExDN,UAAUO,KAAK,GAAG;QAChBnI;QACAoI,kBAAkB3B,mBAAmBC,QAAQ3G;QAC7C0H;QACAC;QACAW,gBAAgB,EAAE;IACpB;IAEA,MAAMC,aACJ,OAAOjI,mBAAmB,WACtB;QAACA;KAAe,GAChBA,eAAe4D,GAAG,CAAC,CAACsE,YAAc,CAAC,CAAC,EAAEA,WAAW;IAEvD,MAAMC,qBACJ,OAAOpB,aAAa,WAAW;QAACA;KAAS,GAAGA,YAAY,EAAE;IAE5D,MAAMjH,0BAA0B,CAC9BT;QAEA,MAAM+I,UAA6C,CAAC;QACpD,IAAIC;QACJ,KAAK,MAAMC,WAAWH,mBAAoB;YACxC,IAAIG,QAAQpJ,UAAU,CAACG,WAAW,MAAM;gBACtC,MAAMkJ,OAAOD,QAAQnJ,KAAK,CAACE,SAAS4C,MAAM,GAAG,GAAG5B,KAAK,CAAC;gBAEtD,4CAA4C;gBAC5C,IAAIkI,KAAKtG,MAAM,KAAK,KAAKsG,IAAI,CAAC,EAAE,KAAK,QAAQ;oBAC3CF,uBAAuBC;oBACvBF,QAAQI,QAAQ,GAAG9J;oBACnB;gBACF;gBAEA,MAAM+J,kBAAkBF,IAAI,CAAC,EAAE,CAACrJ,UAAU,CAAC;gBAC3C,IAAIuJ,iBAAiB;oBACnB,IAAIF,KAAKtG,MAAM,KAAK,KAAKsG,IAAI,CAAC,EAAE,KAAK,QAAQ;wBAC3C,oGAAoG;wBACpG,+HAA+H;wBAC/H,8GAA8G;wBAC9GH,OAAO,CAACG,IAAI,CAAC,EAAE,CAAC,GAAG;4BAAC7J;yBAAa;wBACjC;oBACF;oBACA,iHAAiH;oBACjH,4HAA4H;oBAC5H,0BAA0B;oBAC1B0J,OAAO,CAACG,IAAI,CAAC,EAAE,CAAC,GAAG;wBAAC5J;2BAA8B4J,KAAKpJ,KAAK,CAAC;qBAAG;oBAChE;gBACF;gBAEA,IAAIkJ,wBAAwBD,QAAQI,QAAQ,KAAKD,IAAI,CAAC,EAAE,EAAE;oBACxD,gFAAgF;oBAChF,wEAAwE;oBACxE,MAAMG,yBAAyBJ,QAAQK,QAAQ,CAAC;oBAChD,MAAMC,yBAAyBP,qBAAqBM,QAAQ,CAAC;oBAE7D,IAAID,wBAAwB;wBAQ1B;oBACF,OAAO,IAAI,CAACE,0BAA0B,CAACF,wBAAwB;wBAC7D,6EAA6E;wBAC7E,MAAM,qBAEL,CAFK,IAAIzF,MACR,CAAC,+EAA+E,EAAEoF,qBAAqB,KAAK,EAAEC,QAAQ,gIAAgI,CAAC,GADnP,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEAD,uBAAuBC;gBACvBF,QAAQI,QAAQ,GAAGD,IAAI,CAAC,EAAE;YAC5B;QACF;QAEA,OAAO7E,OAAOwC,OAAO,CAACkC;IACxB;IAEA,MAAMxI,aAA0B,CAACiJ;QAC/B,OAAOzC,mBAAmBC,QAAQwC;IACpC;IAEA,MAAMC,kBAAgC,CAACD;QACrC,OAAOzC,mBAAmBC,QAAQwC;IACpC;IAEA,+DAA+D;IAC/D,0EAA0E;IAC1E,+EAA+E;IAC/E,yEAAyE;IACzE,MAAME,wBAAwB,OAAOC,SAAiBC;QACpD,gGAAgG;QAChG,IAAI,IAAI,CAACC,YAAY,KAAK9E,WAAW;YACnC,IAAI;gBACF,OAAO,AAAC,CAAA,MAAM+E,IAAAA,4BAAa,EAACH,SAASI,KAAK,CAAC,IAAM,IAAI9E,MAAK,EAAGS,GAAG,CAC9DkE;YAEJ,EAAE,OAAOI,GAAG;gBACV,OAAO;YACT;QACF;QACA,MAAMzF,MACJ8C,iBAAiB4C,GAAG,CAAC,IAAI,CAACJ,YAAY,KACtC,IAAIK;QACN,IAAI,CAAC7C,iBAAiB3B,GAAG,CAAC,IAAI,CAACmE,YAAY,GAAG;YAC5CxC,iBAAiB8C,GAAG,CAAC,IAAI,CAACN,YAAY,EAAEtF;QAC1C;QACA,IAAI,CAACA,IAAImB,GAAG,CAACiE,UAAU;YACrBpF,IAAI4F,GAAG,CACLR,SACAG,IAAAA,4BAAa,EAACH,SAASI,KAAK,CAAC,IAAM,IAAI9E;QAE3C;QACA,OAAO,AAAC,CAAA,AAAC,MAAMV,IAAI0F,GAAG,CAACN,YAAa,IAAI1E,KAAI,EAAGS,GAAG,CAACkE;IACrD;IAEA,MAAMpJ,WAAyB,OAAOR;QACpC,MAAMoK,eAAerD,mBAAmBC,QAAQhH;QAEhD,MAAMqK,gBAAgBD,aAAaE,WAAW,CAACnD,aAAI,CAACC,GAAG;QACvD,MAAMuC,UAAUS,aAAatK,KAAK,CAAC,GAAGuK;QACtC,MAAME,WAAWH,aAAatK,KAAK,CAACuK,gBAAgB;QAEpD,IAAIG;QAEJ,KAAK,MAAMC,OAAO7B,WAAY;YAC5B,MAAM8B,4BAA4B,GAAGN,eAAeK,KAAK;YACzD,IACE,CAACD,UACA,MAAMd,sBAAsBC,SAAS,GAAGY,WAAWE,KAAK,GACzD;gBACAD,SAASE;YACX;YACA,uEAAuE;YACvE,6DAA6D;YAC7D,IAAI,CAACC,oBAAoB,CAACD;QAC5B;QAEA,OAAOF;IACT;IAEA,MAAM9J,mBAAqC,OACzCiJ,SACAY,UACAK;QAEA,MAAMC,cAAc9D,mBAAmBC,QAAQ2C;QAE/C,IAAIa;QAEJ,KAAK,MAAMC,OAAOG,KAAM;YACtB,kGAAkG;YAClG,MAAME,kBAAkB,GAAGP,SAAS,CAAC,EAAEE,KAAK;YAC5C,MAAMC,4BAA4B,GAAGG,cAAc1D,aAAI,CAACC,GAAG,GAAG0D,iBAAiB;YAC/E,IAAI,CAACN,UAAW,MAAMd,sBAAsBC,SAASmB,kBAAmB;gBACtEN,SAASE;YACX;YACA,uEAAuE;YACvE,6DAA6D;YAC7D,IAAI,CAACC,oBAAoB,CAACD;QAC5B;QAEA,OAAOF;IACT;IAEA,IAAIO,IAAAA,gCAAe,EAAC5I,OAAO;QACzB,OAAO6I,IAAAA,sCAAkB,EAAC;YACxBhE;YACA,8EAA8E;YAC9E1G,MAAMkH,cAAclH,IAAI;YACxB6B;YACA9B;YACAoJ;YACA9I;YACAmH;QACF;IACF;IAEA,IAAImD,iBAAiB,MAAM7K,uBAAuBC,UAAU;QAC1DC;QACAC;QACAC;QACAE;QACAD;QACAyK,eAAe,IAAI;QACnBvK;QACAC;QACAC;QACAC;IACF;IAEA,MAAMqK,uBACJ7K,SAASY,2CAAgC,IACzC,CAAC,CAAC+J,eAAevJ,cAAc,IAC/BZ;IAEF,IAAI,CAACmK,eAAezJ,UAAU,IAAI,CAAC2J,sBAAsB;QACvD,IAAI,CAACtD,OAAO;YACV,8DAA8D;YAC9DuD,KAAInM,KAAK,CACP,GAAGoM,IAAAA,gBAAI,EACLhL,SAAS6G,OAAO,CAAC,GAAG5F,yBAAa,CAAC,CAAC,CAAC,EAAE,KACtC,uFAAuF,CAAC;YAE5FgK,QAAQC,IAAI,CAAC;QACf,OAAO;gBA2BkBlE;YA1BvB,2CAA2C;YAC3C,MAAM,CAACmE,mBAAmBC,eAAe,GAAG,MAAMC,IAAAA,kCAAgB,EAAC;gBACjE1E,QAAQA;gBACR2E,KAAKhE;gBACLC,cAAcA;gBACdvH;gBACAM;YACF;YACA,IAAI,CAAC6K,mBAAmB;gBACtB,IAAII,UAAU,GAAGP,IAAAA,gBAAI,EACnBhL,SAAS6G,OAAO,CAAC,GAAG5F,yBAAa,CAAC,CAAC,CAAC,EAAE,KACtC,6BAA6B,CAAC;gBAEhC,IAAImK,gBAAgB;wBAEF;oBADhBG,WAAW,CAAC,mBAAmB,EAAEP,IAAAA,gBAAI,EACnClE,aAAI,CAAC0E,QAAQ,CAAC,EAAA,kBAAA,IAAI,CAACC,SAAS,qBAAd,gBAAgBC,OAAO,KAAI,IAAIN,iBAC7C,kCAAkC,CAAC;gBACvC,OAAO;oBACLG,WACE;gBACJ;gBAEA,MAAM,qBAAkB,CAAlB,IAAIhI,MAAMgI,UAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAiB;YACzB;YAEA,mEAAmE;YACnE,IAAI,IAAI,CAAC/B,YAAY,GAAExC,wBAAAA,iBAAiB4C,GAAG,CAAC,IAAI,CAACJ,YAAY,sBAAtCxC,sBAAyC2E,KAAK;YACrEf,iBAAiB,MAAM7K,uBAAuBC,UAAU;gBACtDC;gBACAC;gBACAC;gBACAE;gBACAD;gBACAyK,eAAe,IAAI;gBACnBvK;gBACAC;gBACAC;gBACAC;YACF;QACF;IACF;IAEA,MAAMd,WAAW,IAAIiM,4CAAqB,GAAGC,SAAS,CAAC5L;IAEvD,iGAAiG;IACjG,6GAA6G;IAC7G,MAAM6L,OAAO,MAAMC,IAAAA,8BAAc,EAC/B,YACA;QACEC,qBAAqB/L;QACrBgM,yBAAyBtM;QACzBuM,yBAAyBtB,eAAexJ,WAAW;IACrD,GACA;QACE+K,MAAMvB,eAAe5E,QAAQ;QAC7B9E,OAAO0J,eAAe1J,KAAK;QAC3BkL,sBAAsB;QACtB,0FAA0F;QAC1FC,yBAAyB;IAC3B;IAGF,6DAA6D;IAC7D,MAAMC,SAAS9L,sBACZ0D,GAAG,CAAC,CAAC,CAAChB,SAASqJ,WAAW;QACzB,OAAO,CAAC,MAAM,EAAErJ,QAAQ,2CAA2C,EAAEE,KAAKC,SAAS,CACjFkJ,YACA,IAAI,CAAC;IACT,GACCnK,IAAI,CAAC;IAER,OAAOkK,SAASR;AAClB;MAEA,YAAe5E", "ignoreList": [0]}