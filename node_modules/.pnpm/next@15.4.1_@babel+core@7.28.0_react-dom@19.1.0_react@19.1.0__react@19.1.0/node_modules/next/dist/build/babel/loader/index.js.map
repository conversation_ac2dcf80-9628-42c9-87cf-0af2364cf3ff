{"version": 3, "sources": ["../../../../src/build/babel/loader/index.ts"], "sourcesContent": ["import type { Span } from '../../../trace'\nimport transform from './transform'\nimport type { NextJsLoaderContext } from './types'\n\nasync function nextBabelLoader(\n  this: NextJsLoaderContext,\n  parentTrace: Span,\n  inputSource: string,\n  inputSourceMap: object | null | undefined\n) {\n  const filename = this.resourcePath\n\n  // Ensure `.d.ts` are not processed.\n  if (filename.endsWith('.d.ts')) {\n    return [inputSource, inputSourceMap]\n  }\n\n  const target = this.target\n  const loaderOptions: any = parentTrace\n    .traceChild('get-options')\n    // @ts-ignore TODO: remove ignore once webpack 5 types are used\n    .traceFn(() => this.getOptions())\n\n  if (loaderOptions.exclude && loaderOptions.exclude(filename)) {\n    return [inputSource, inputSourceMap]\n  }\n\n  const loaderSpanInner = parentTrace.traceChild('next-babel-turbo-transform')\n  const { code: transformedSource, map: outputSourceMap } =\n    await loaderSpanInner.traceAsyncFn(\n      async () =>\n        await transform.call(\n          this,\n          inputSource,\n          inputSourceMap,\n          loaderOptions,\n          filename,\n          target,\n          loaderSpanInner\n        )\n    )\n\n  return [transformedSource, outputSourceMap]\n}\n\nconst nextBabelLoaderOuter = function nextBabelLoaderOuter(\n  this: NextJsLoaderContext,\n  inputSource: string,\n  inputSourceMap: object | null | undefined\n) {\n  const callback = this.async()\n\n  const loaderSpan = this.currentTraceSpan.traceChild('next-babel-turbo-loader')\n  loaderSpan\n    .traceAsyncFn(() =>\n      nextBabelLoader.call(this, loaderSpan, inputSource, inputSourceMap)\n    )\n    .then(\n      ([transformedSource, outputSourceMap]: any) =>\n        callback?.(null, transformedSource, outputSourceMap || inputSourceMap),\n      (err) => {\n        callback?.(err)\n      }\n    )\n}\n\nexport default nextBabelLoaderOuter\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parentTrace", "inputSource", "inputSourceMap", "filename", "resourcePath", "endsWith", "target", "loaderOptions", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "getOptions", "exclude", "loaderSpanInner", "code", "transformedSource", "map", "outputSourceMap", "traceAsyncFn", "transform", "call", "nextBabelLoaderOuter", "callback", "async", "loaderSpan", "currentTraceSpan", "then", "err"], "mappings": ";;;;+BAkEA;;;eAAA;;;kEAjEsB;;;;;;AAGtB,eAAeA,gBAEbC,WAAiB,EACjBC,WAAmB,EACnBC,cAAyC;IAEzC,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,oCAAoC;IACpC,IAAID,SAASE,QAAQ,CAAC,UAAU;QAC9B,OAAO;YAACJ;YAAaC;SAAe;IACtC;IAEA,MAAMI,SAAS,IAAI,CAACA,MAAM;IAC1B,MAAMC,gBAAqBP,YACxBQ,UAAU,CAAC,cACZ,+DAA+D;KAC9DC,OAAO,CAAC,IAAM,IAAI,CAACC,UAAU;IAEhC,IAAIH,cAAcI,OAAO,IAAIJ,cAAcI,OAAO,CAACR,WAAW;QAC5D,OAAO;YAACF;YAAaC;SAAe;IACtC;IAEA,MAAMU,kBAAkBZ,YAAYQ,UAAU,CAAC;IAC/C,MAAM,EAAEK,MAAMC,iBAAiB,EAAEC,KAAKC,eAAe,EAAE,GACrD,MAAMJ,gBAAgBK,YAAY,CAChC,UACE,MAAMC,kBAAS,CAACC,IAAI,CAClB,IAAI,EACJlB,aACAC,gBACAK,eACAJ,UACAG,QACAM;IAIR,OAAO;QAACE;QAAmBE;KAAgB;AAC7C;AAEA,MAAMI,uBAAuB,SAASA,qBAEpCnB,WAAmB,EACnBC,cAAyC;IAEzC,MAAMmB,WAAW,IAAI,CAACC,KAAK;IAE3B,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAAChB,UAAU,CAAC;IACpDe,WACGN,YAAY,CAAC,IACZlB,gBAAgBoB,IAAI,CAAC,IAAI,EAAEI,YAAYtB,aAAaC,iBAErDuB,IAAI,CACH,CAAC,CAACX,mBAAmBE,gBAAqB,GACxCK,4BAAAA,SAAW,MAAMP,mBAAmBE,mBAAmBd,iBACzD,CAACwB;QACCL,4BAAAA,SAAWK;IACb;AAEN;MAEA,WAAeN", "ignoreList": [0]}