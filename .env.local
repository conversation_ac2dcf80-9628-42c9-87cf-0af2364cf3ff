# =============================================================================
# BEDAZZLED FRONTEND ENVIRONMENT VARIABLES
# =============================================================================
# Copy this file to .env.local and fill in your actual values
# Never commit .env.local to version control

# -----------------------------------------------------------------------------
# API Configuration
# -----------------------------------------------------------------------------
# Backend API URL (adjust port if your backend runs on different port)
NEXT_PUBLIC_API_URL=http://localhost:3001/api

# Frontend URL (used for redirects and CORS)
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000

# -----------------------------------------------------------------------------
# Google OAuth Configuration
# -----------------------------------------------------------------------------
# Get these from Google Cloud Console:
# 1. Go to https://console.cloud.google.com/
# 2. Create a new project or select existing one
# 3. Enable Google+ API
# 4. Go to Credentials > Create Credentials > OAuth 2.0 Client IDs
# 5. Set authorized redirect URIs to: http://localhost:3001/api/auth/google/callback

NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-client-id-here.apps.googleusercontent.com

# -----------------------------------------------------------------------------
# File Upload Configuration
# -----------------------------------------------------------------------------
# Maximum file size for uploads (in bytes) - 10MB default
NEXT_PUBLIC_MAX_FILE_SIZE=10485760

# Allowed file types for uploads
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# -----------------------------------------------------------------------------
# Application Configuration
# -----------------------------------------------------------------------------
# Environment (development, staging, production)
NODE_ENV=development

# Application name and version
NEXT_PUBLIC_APP_NAME=Bedazzled
NEXT_PUBLIC_APP_VERSION=1.0.0

# -----------------------------------------------------------------------------
# Analytics and Monitoring (Optional)
# -----------------------------------------------------------------------------
# Google Analytics ID (optional)
# NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX

# Sentry DSN for error tracking (optional)
# NEXT_PUBLIC_SENTRY_DSN=https://your-sentry-dsn-here

# -----------------------------------------------------------------------------
# Payment Configuration (for future implementation)
# -----------------------------------------------------------------------------
# Stripe publishable key (when implementing payments)
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key_here

# -----------------------------------------------------------------------------
# Feature Flags
# -----------------------------------------------------------------------------
# Enable/disable features during development
NEXT_PUBLIC_ENABLE_CUSTOM_ORDERS=true
NEXT_PUBLIC_ENABLE_WISHLIST=true
NEXT_PUBLIC_ENABLE_REVIEWS=true
NEXT_PUBLIC_ENABLE_ADMIN_DASHBOARD=true

# -----------------------------------------------------------------------------
# SEO and Social Media
# -----------------------------------------------------------------------------
# Default meta information
NEXT_PUBLIC_SITE_NAME=Bedazzled - Rhinestone Bedazzled Portraits
NEXT_PUBLIC_SITE_DESCRIPTION=Transform your memories into stunning rhinestone bedazzled portraits
NEXT_PUBLIC_SITE_URL=https://bedazzled.com

# Social media handles (optional)
# NEXT_PUBLIC_TWITTER_HANDLE=@bedazzled
# NEXT_PUBLIC_INSTAGRAM_HANDLE=@bedazzled
# NEXT_PUBLIC_FACEBOOK_PAGE=bedazzled

# -----------------------------------------------------------------------------
# Development Configuration
# -----------------------------------------------------------------------------
# Enable debug mode for development
NEXT_PUBLIC_DEBUG_MODE=true

# Show development tools
NEXT_PUBLIC_SHOW_DEV_TOOLS=true
